import React, { useEffect } from "react";
import Layout from "@layout/sideMenuContainer";
import { NextPage } from "next";
import {
  CommonDataInterface,
  CommunicationConfigsInterface,
} from "@interfaces/common.inteface";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import EditProfile from "@features/profile/editProfile/EditProfile";
import useAppRouter from "@features/common/router.context";
import { useTranslation } from "next-i18next";
import { ASIDE_PAGES, CHANNEL_TYPE, FLOW_TYPE } from "@constants/common";
import { BasicCartData } from "@features/cart/interfaces/cart.interface";
import commonPagesMiddleware from "pages/__middleware/commonPages.middleware";
import getConfig from "next/config";
import GiftsOrdered from "@features/orders/giftsOrdered/GiftsOrdered";
import GiftsOrderedDisplay from "@features/orders/giftsOrdered/giftOrderedDisplay/GiftsOrderedDisplay";
import TabSelectionHeader from "@features/common/header/tabSelectionHeader/TabSelectionHeader";
import {
  WebConfigurables,
  getBlacklistedCountries,
  communicationConfigs,
  getCaptchaConfig,
} from "@features/common/commonAPI";
import SavedCards from "@features/savedCards/SavedCards";

const {
  publicRuntimeConfig: { authRedirectUrl, ecomHostDomain },
} = getConfig();

interface ProfileInterface {
  commonData: CommonDataInterface;
  slug: string;
  region: string;
  isUserSignedIn: boolean;
  idToken: string;
  accessToken: string;
  refreshToken: string;
  displayName: string;
  userAttributes: any;
  basicCartData: BasicCartData;
  WebConfigurablesData: any;
  isCaptchaRequired: boolean;
  blacklistedCountries: any;
  isGuestUser: boolean;
  communicationConfigsData: CommunicationConfigsInterface;
}
const Profile: NextPage<ProfileInterface> = ({
  commonData,
  isGuestUser,
  slug,
  isUserSignedIn,
  WebConfigurablesData,
  isCaptchaRequired,
  blacklistedCountries,
  communicationConfigsData,
}): JSX.Element => {
  const { t } = useTranslation("common");
  const { router } = useAppRouter();
  const footer = commonData?.data?.footer;
  const stores = commonData?.data?.stores;
  const headers = commonData?.data?.headers;
  const siteConfigs = commonData?.data?.siteConfigs;

  const menuData = {
    heading: t("myAccount"),
    menu: [
      {
        name: t("profile"),
        value: "profile",
        url: `/account/profile`,
      },
      {
        name: t("orders"),
        value: "ordered",
        url: `/account/orders`,
      },
      {
        name: t("savedCards"),
        value: "saved-cards",
        url: `/account/cards`,
      },
    ],
  };

  useEffect(() => {
    if (isUserSignedIn == false) {
      router.push({
        pathname: `/`,
      });
    }
  }, [isUserSignedIn]);

  const homeMetaDatas = siteConfigs?.edges[0]?.node?.homepageSiteMeta;
  const solutionHubEnabled = siteConfigs?.edges[0]?.node?.solutionHubEnabled;

  const summary = router?.query?.orderID;

  return (
    <TabSelectionHeader
      footerData={footer}
      headerData={headers?.edges}
      storesDataItem={stores?.edges}
      disableStoreSelection={true}
      isUserSignedIn={isUserSignedIn}
      isGuestUser={isGuestUser}
      solutionHubEnabled={solutionHubEnabled}
    >
      {isUserSignedIn && (
        <Layout
          siteConfigData={siteConfigs?.edges[0]}
          footerData={footer}
          metaData={homeMetaDatas}
          majorHeaderData={headers?.edges}
          sideMenu={menuData}
          storesDataItem={commonData?.data?.stores?.edges}
          isUserSignedIn={isUserSignedIn}
          page={
            slug == "orders"
              ? ASIDE_PAGES.ORDERED
              : slug == "profile"
              ? ASIDE_PAGES.EDIT_PROFILE
              : ASIDE_PAGES.SAVED_CARDS
          }
        >
          <div className="profile">
            {slug == "profile" && (
              <EditProfile
                dropdownData={menuData}
                WebConfigurablesData={WebConfigurablesData}
                isCaptchaRequired={isCaptchaRequired}
                blacklistedCountries={blacklistedCountries}
                communicationConfigsData={communicationConfigsData?.data}
              />
            )}

            {slug == "orders" && (
              <div>
                {summary ? (
                  <GiftsOrderedDisplay dropdownData={menuData} />
                ) : (
                  <GiftsOrdered dropdownData={menuData} />
                )}
              </div>
            )}

            {slug == "cards" && <SavedCards/>}
          </div>
        </Layout>
      )}
    </TabSelectionHeader>
  );
};

const redirectToLogin = (locale: any, url: string) => {
  return {
    redirect: {
      destination: `${authRedirectUrl}/${
        locale.split("-")[0]
      }/login/?rdir=${ecomHostDomain}/shop/${locale}/account/profile/`,
      permanent: false,
    },
  };
};

const userExists = (request: any) => {
  if (request?.cookies?.REFRESH_TOKEN && request?.cookies?.REFRESH_TOKEN.length)
    return true;
  return false;
};

export async function getServerSideProps(params: any) {
  // #. If user doesn't redirect to login
  if (!userExists(params.req)) {
    return redirectToLogin(params.locale, params?.req?.headers.host);
  }

  //  #. Get page common data and responses from the __middleware
  const pageData = await commonPagesMiddleware(params);

  // #. Config call for update mobile number enable or not
  const WebConfigurablesData: any = await WebConfigurables(
    params?.defaultLocale
  );

  if (!pageData.data) {
    return pageData;
  }

  const {
    languageReource,
    region,
    refreshToken,
    idToken,
    isUserSignedIn,
    accessToken,
    displayName,
    isGuestUser,
    commonData,
    cartData,
    userAttributes,
    localeCode,
  } = pageData.data;

  // #. fetch captcha config, communicationConfig data for handling resend OTP CTA and blacklisted countries
  const [
    captchaConfigData,
    communicationConfigsData,
    { blacklistedCountries },
  ] = await Promise.all([
    getCaptchaConfig(localeCode),
    communicationConfigs(
      localeCode,
      region.toUpperCase(),
      FLOW_TYPE.CHANGE_PHONE_NUMBER,
      CHANNEL_TYPE.WHATSAPP
    ),
    getBlacklistedCountries(localeCode),
  ]);

  const { slug } = params.query;

  return {
    props: {
      ...(await serverSideTranslations(languageReource, ["common"])),
      commonData,
      slug,
      region,
      isUserSignedIn,
      idToken,
      accessToken,
      refreshToken,
      displayName,
      isGuestUser,
      userAttributes,
      basicCartData: cartData ? cartData?.data?.cart : {},
      WebConfigurablesData,
      isCaptchaRequired: captchaConfigData || {},
      blacklistedCountries: blacklistedCountries || [],
      communicationConfigsData: communicationConfigsData || {},
    },
  };
}

export default Profile;
