import type { NextPage } from "next";
import { useEffect } from "react";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import { CommonDataInterface } from "@interfaces/common.inteface";
import CommonLayout from "@layout/common";
import useBrandAPI from "@features/brand/brandAPI";
import commonPagesMiddleware from "./__middleware/commonPages.middleware";
import TabSelectionHeader from "@features/common/header/tabSelectionHeader/TabSelectionHeader";

interface WorkInterface {
  commonData: CommonDataInterface;
  isUserSignedIn: boolean;
  displayName: string;
  isGuestUser: boolean;
}

const Work: NextPage<WorkInterface> = ({
  commonData,
  isUserSignedIn,
  displayName,
  isGuestUser,
}): JSX.Element => {
  const { clearLocalStore } = useBrandAPI();

  useEffect(() => {
    // #. Clear brand session storage, if any pending item in the store
    clearLocalStore();
  }, []);

  // data values
  const headers = commonData?.data?.headers;
  const siteConfigs = commonData?.data?.siteConfigs;
  const stores = commonData?.data?.stores;
  const footer = commonData?.data?.footer;
  const homeMetaDatas = siteConfigs?.edges[0]?.node?.homepageSiteMeta;
  const solutionHubEnabled = siteConfigs?.edges[0]?.node?.solutionHubEnabled;

  return (
    <TabSelectionHeader
      headerData={headers?.edges}
      storesDataItem={stores?.edges}
      // disableStoreSelection={disableStoreSelection}
      isUserSignedIn={isUserSignedIn}
      isGuestUser={isGuestUser}
      solutionHubEnabled={solutionHubEnabled}
    >
      <CommonLayout
        minorHeaderData={headers?.edges}
        siteConfigData={siteConfigs?.edges[0]}
        storesDataItem={stores?.edges}
        majorHeaderData={headers?.edges}
        metaData={homeMetaDatas}
        footerData={footer}
        page="work"
        isUserSignedIn={isUserSignedIn}
        displayName={displayName}
        isCrawlable={true}
        isGuestUser={isGuestUser}
      >
        <div className="content"></div>
      </CommonLayout>
    </TabSelectionHeader>
  );
};

export async function getServerSideProps(params: any) {
  //  #. Get page common data and responses from the __middleware
  const pageData = await commonPagesMiddleware(params);

  if (!pageData.data) {
    return pageData;
  }

  const {
    languageReource,
    isUserSignedIn,
    displayName,
    isGuestUser,
    commonData,
    userAttributes
  } = pageData.data;

  return {
    props: {
      ...(await serverSideTranslations(languageReource, ["common"])),
      commonData,
      isUserSignedIn,
      displayName,
      isGuestUser,
      userAttributes: userAttributes || {}
    },
  };
}

export default Work;
