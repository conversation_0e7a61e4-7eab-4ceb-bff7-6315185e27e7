import { OFFER_TYPE } from "@constants/common";
import { HTTP_STATUS_CODE } from "@constants/statusCodes";
import { BrandContextProvider } from "@features/brand/brand.context";
import TabSelectionHeader from "@features/common/header/tabSelectionHeader/TabSelectionHeader";
import OfferInfoPageLayout from "@features/offers/OfferInfoPageLayout/OfferInfoPageLayout";
import { getDefaultBrandOffers, getDefaultBrandPromos, getDefaultStoreBrand, getPromoCodes } from "@features/offers/offersAPI";
import CommonLayout from "@layout/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import commonPagesMiddleware from "pages/__middleware/commonPages.middleware";

const OffersPage = ({
  commonData,
  secureConfigs,
  brandSlug,
  isUserSignedIn,
  displayName,
  isGuestUser,
  brandData,
  recommendedGiftCardsData,
  brandOffers,
  brandPromos,
  offerSlug,
  brandOfferData
}: any) => {
  const headers = commonData?.data?.headers;
  const siteConfigs = commonData?.data?.siteConfigs;
  const stores = commonData?.data?.stores;
  const footer = commonData?.data?.footer;
  const siteMeta  = brandOfferData?.offerData?.node?.offerDetailPageSiteMeta;
  const solutionHubEnabled = siteConfigs?.edges[0]?.node?.solutionHubEnabled;

  return (
    <TabSelectionHeader
      headerData={headers?.edges}
      storesDataItem={stores?.edges}
      isUserSignedIn={isUserSignedIn}
      isGuestUser={isGuestUser}
      solutionHubEnabled={solutionHubEnabled}
    >
      <CommonLayout
        minorHeaderData={headers?.edges}
        siteConfigData={siteConfigs?.edges[0]}
        storesDataItem={stores?.edges}
        majorHeaderData={headers?.edges}
        metaData={siteMeta}
        footerData={footer}
        page="offers"
        isUserSignedIn={isUserSignedIn}
        displayName={displayName}
        isCrawlable={true}
        isGuestUser={isGuestUser}
      >
        <BrandContextProvider
          cameraTagAppId={secureConfigs?.cameraTagAppId}
          imglyLicense={secureConfigs?.imglyLicense}
        >
          <div className="offer-page">
            <OfferInfoPageLayout
              brandSlug={brandSlug}
              brandData={brandData}
              recommendedGiftCardsData={recommendedGiftCardsData}
              displayName={displayName}
              brandOffers={brandOffers}
              brandPromos={brandPromos}
              offerSlug={offerSlug}
              offerSlugData={brandOfferData}
            />
          </div>
        </BrandContextProvider>
      </CommonLayout>
    </TabSelectionHeader>

  )
}

  /**
   * Method to prepopulate offer data if slug is present
   */
  const prePopulateData = async (offerSlug: any, brandOffers: any, brandPromos: any, locale: string, code: string) => {
    try {
      if (offerSlug) {
        const offerFound = brandOffers.filter((offer: any) => {
          return offer?.node?.brand?.slug == offerSlug
        });
        if (offerFound && offerFound.length) {
          return {
            offerType : OFFER_TYPE.EXTRA_VALUE,
            offerData : offerFound[0]
          }
        } else {
          const offerFound = brandPromos.filter((offer: any) => {
            return offer?.node?.brand?.slug == offerSlug
          });
          if (offerFound && offerFound.length) {
            const promoData = await getPromoCodes(locale, offerFound[0]?.node?.brand?.code, code);
            return {
              offerType : OFFER_TYPE.PROMO_CODE,
              promoData : promoData,
              offerData: offerFound[0]
            }
          }
        }
      }
    } catch (error) {
      console.log('Error occured in prePopulateData ', error);
    }

  }

export async function getServerSideProps(params: any) {
  const {query : {slug}} = params;

  const pageData = await commonPagesMiddleware(params);

  if (!pageData.data) {
    return pageData;
  }

  const {
    region,
    localeCode,
    languageReource,
    refreshToken,
    idToken,
    isUserSignedIn,
    accessToken,
    displayName,
    isGuestUser,
    commonData,
    cartData,
    userAttributes,
  } = pageData.data;

  const [brandData, brandOffers, brandPromos] = await Promise.all([getDefaultStoreBrand(region, localeCode), getDefaultBrandOffers(region, localeCode), getDefaultBrandPromos(region, localeCode)]);

  console.log('brandData ', brandData);
  const slugData = await prePopulateData(slug, brandOffers?.data?.defaultStoreBrandOffers?.edges, brandPromos?.data?.defaultStoreBrandPromoCodes?.edges, localeCode, brandData?.data?.defaultStoreBrand?.code);
  console.log('slugData ', slugData);
  // #. If brand not found redirect to 404 page
  if (
    brandData?.data?.brand?.status === HTTP_STATUS_CODE.INVALID_OR_NOT_FOUND
  ) {
    return {
      redirect: {
        destination: `/${localeCode}-${region}/404/`,
        permanent: false,
      },
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(languageReource, ["common"])),
      commonData,
      isUserSignedIn,
      idToken,
      accessToken,
      refreshToken,
      displayName,
      isGuestUser,
      userAttributes: userAttributes || {},
      basicCartData: cartData ? cartData?.data?.cart : {},
      brandData: brandData?.data?.defaultStoreBrand || {},
      brandOffers: brandOffers?.data?.defaultStoreBrandOffers?.edges || [],
      brandPromos: brandPromos?.data?.defaultStoreBrandPromoCodes?.edges || [],
      offerSlug: slug || "",
      brandOfferData: slugData
    },
  };
}

export default OffersPage;