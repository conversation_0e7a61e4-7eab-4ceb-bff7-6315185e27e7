import { HTTP_STATUS_CODE } from "@constants/statusCodes";
import { BrandContextProvider } from "@features/brand/brand.context";
import TabSelectionHeader from "@features/common/header/tabSelectionHeader/TabSelectionHeader";
import OfferPageLayout from "@features/offers/OfferPageLayout/OfferPageLayout";
import { getDefaultBrandOffers, getDefaultBrandPromos, getDefaultStoreBrand } from "@features/offers/offersAPI";
import CommonLayout from "@layout/common";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import commonPagesMiddleware from "pages/__middleware/commonPages.middleware";

const OffersPage = ({
  commonData,
  secureConfigs,
  brandSlug,
  isUserSignedIn,
  displayName,
  isGuestUser,
  brandData,
  recommendedGiftCardsData,
  brandOffers,
  brandPromos,
  offerSlug
}: any) => {
  const headers = commonData?.data?.headers;
  const siteConfigs = commonData?.data?.siteConfigs;
  const stores = commonData?.data?.stores;
  const footer = commonData?.data?.footer;
  const offersMetaData =
  brandData?.offerPageSiteMeta;
  const solutionHubEnabled = siteConfigs?.edges[0]?.node?.solutionHubEnabled;

  return (
    <TabSelectionHeader
    footerData={footer}
      headerData={headers?.edges}
      storesDataItem={stores?.edges}
      isUserSignedIn={isUserSignedIn}
      isGuestUser={isGuestUser}
      page={"offers"}
      solutionHubEnabled={solutionHubEnabled}
    >
      <CommonLayout
        minorHeaderData={headers?.edges}
        siteConfigData={siteConfigs?.edges[0]}
        storesDataItem={stores?.edges}
        majorHeaderData={headers?.edges}
        metaData={offersMetaData}
        footerData={footer}
        page="offers"
        isUserSignedIn={isUserSignedIn}
        displayName={displayName}
        isCrawlable={true}
        isGuestUser={isGuestUser}
      >
        <BrandContextProvider
          cameraTagAppId={secureConfigs?.cameraTagAppId}
          imglyLicense={secureConfigs?.imglyLicense}
        >
          <div className="offer-page">
            <OfferPageLayout
              brandSlug={brandSlug}
              brandData={brandData}
              recommendedGiftCardsData={recommendedGiftCardsData}
              displayName={displayName}
              brandOffers={brandOffers}
              brandPromos={brandPromos}
              offerSlug={offerSlug}
            />
          </div>
        </BrandContextProvider>
      </CommonLayout>
    </TabSelectionHeader>

  )
}

export async function getServerSideProps(params: any) {
  const {query : {slug}} = params;

  const pageData = await commonPagesMiddleware(params);

  if (!pageData.data) {
    return pageData;
  }

  const {
    region,
    localeCode,
    languageReource,
    refreshToken,
    idToken,
    isUserSignedIn,
    accessToken,
    displayName,
    isGuestUser,
    commonData,
    cartData,
    userAttributes,
  } = pageData.data;

  const [brandData, brandOffers, brandPromos] = await Promise.all([getDefaultStoreBrand(region, localeCode), getDefaultBrandOffers(region, localeCode), getDefaultBrandPromos(region, localeCode)]);

  // #. If brand not found redirect to 404 page
  if (
    brandData?.data?.brand?.status === HTTP_STATUS_CODE.INVALID_OR_NOT_FOUND
  ) {
    return {
      redirect: {
        destination: `/${localeCode}-${region}/404/`,
        permanent: false,
      },
    };
  }

  return {
    props: {
      ...(await serverSideTranslations(languageReource, ["common"])),
      commonData,
      isUserSignedIn,
      idToken,
      accessToken,
      refreshToken,
      displayName,
      isGuestUser,
      userAttributes: userAttributes || {},
      basicCartData: cartData ? cartData?.data?.cart : {},
      brandData: brandData?.data?.defaultStoreBrand || {},
      brandOffers: brandOffers?.data?.defaultStoreBrandOffers?.edges || [],
      brandPromos: brandPromos?.data?.defaultStoreBrandPromoCodes?.edges || [],
      offerSlug: slug || ""
    },
  };
}

export default OffersPage;