@import "@styles/variables";
@import "@styles/mixins";

.brand-offers {
  position: relative;


  h3 {
    @include font-size-important(16);

    margin-bottom: 10px;
  }

  &__offer-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 16px;
    min-height: 76px;
    max-height: 343px;
    overflow-y: scroll;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: $semi-dark-grey1;
      border-radius: 17px;
    }

    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: $semi-dark-grey1 transparent;

    @media (max-width: ($lg + 40)) {
      width: 409px;
    }

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }

    @media (max-width: ($md + 40)) {
      width: 100%;
    }
  }
}

.brand-offers__swiper {
  background-color: transparent;
  width: 455px;
  position: relative;

  @media (max-width: ($lg + 40)) {
    width: 409px;
  }

  @media (max-width: ($md + 40)) {
    width: 100%;
  }
}

.brand-offers__swiper-slide {
  height: 115px;
  width: 105px;
  cursor: pointer;

  &-top {
    background-color: transparent;
    position: relative;

    &-img {
      position: absolute;
      top: -20px;
      left: 50%;
      transform: translateX(-50%);

      img {
        width: 43px;
        height: 43px;
        border-radius: 12px;
        border: 3px solid $grey-bg;
      }
    }

    &-text {
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      background-color: $white;
      margin-top: 20px;
      padding: 20px 10px 10px;
      word-break: break-word;
      color: $dark-purple;
      font-weight: 500;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;

      @include font-size(12);
    }
  }

  &-bottom {
    background-color: $error-red;
    color: $white;
    position: relative;
    padding: 10px 5px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    display: flex;
    align-items: center;

    &-img {
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
    }

    &-text {
      text-align: center;
      width: 100%;
      display: inline-block;
      font-weight: 600;
      text-transform: uppercase;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      @include font-size(12);
    }
  }
}

.swiper-nav-wrapper {
  position: absolute;
  right: 25px;
  top: 0;
  display: flex;
  justify-content: space-between;
  z-index: 2;
  gap: 0;
  background-color: $white;
  border-radius: 20px;

  @include rtl-styles {
    left: 25px;
    right: auto;
  }
}

.next,
.prev {
  color: $black-header;
  cursor: pointer;
  position: absolute;
  z-index: 99;

  i {
    @include font-size(24);
  }
}

.next {
  right: -21px;
  top: 60%;
  transform: translateY(-50%);
}

.prev {
  top: 60%;
  left: -20px;
  transform: translateY(-50%);

  img {
    transform: rotate(180deg);
  }
}