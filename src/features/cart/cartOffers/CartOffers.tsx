import dynamic from "next/dynamic";
import styles from "./CartOffers.module.scss";
import {useState } from "react";
import {useTranslation } from "next-i18next";
import HappyYouCard from "@features/brand/brandOffers/happyYouCard/HappyYouCard";
import { IBrandOffersNode } from "@features/brand/interfaces/brandOffers.interface";
const BrandOfferDialog = dynamic(
  () => import("@features/brand/brandOfferDialog/BrandOfferDialog")
);

/**
 * @method CartOffers
 * @description Offer details slider
 * @returns {JSX.Element}
 */
const CartOffers = ({cartOffersData}:any): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  
  const [selectedOfferData, setSelectedOfferData] = useState<
    IBrandOffersNode | false
  >(false);

  const extraValueOffers = cartOffersData?.extraOffers?.edges || [];

  /**
   * @method onOfferClicked
   * @param offer
   */
  const onOfferClicked = (offer: IBrandOffersNode) => {
    setSelectedOfferData(offer);
  };

  return (
    <div className={`${styles["brand-offers"]} brand-offers`}>
      <div className={`${styles["brand-offers__offer-card"]}`}>
        {extraValueOffers && extraValueOffers?.length > 0 &&
          extraValueOffers.map((offer: any, index:number) => (
             <HappyYouCard key={index} item={offer} onOfferClicked={onOfferClicked}/>
          ))}
      </div>
      {selectedOfferData && (
        <BrandOfferDialog
          onClose={() => setSelectedOfferData(false)}
          offerData={selectedOfferData}
          isGeneric={false}
        />
      )}
    </div>
  );
};

export default CartOffers;
