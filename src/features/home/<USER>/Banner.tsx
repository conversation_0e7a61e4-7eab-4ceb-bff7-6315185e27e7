import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
// import Swiper core and required modules
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import styles from "./Banner.module.scss";
import getConfig from "next/config";
import getDirection from "@utils/getDirection";
import { BannerItems } from "@interfaces/common.inteface";
import { Dialog, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { BANNER_TYPE, PLATFORM_TYPE } from "@constants/common";
import DateTime from "@utils/dateTime";
import { useQuery } from "@apollo/client";
import { BannerInterface } from "@features/home/<USER>/banner.interface";
import BannerSkeleton from "../contentLoader/bannerSkeleton/BannerSkeleton";
import { BANNER_QUERY } from "./banner.query";
import useAppRouter from "@features/common/router.context";



// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method Banner
 * @description banner wrapper component
 * @returns {JSX.Element}
 */

const Banner = (): JSX.Element => {
  // getting locale
  const {
    state: { region, locale },
    router,
  } = useAppRouter();
  const defaultRegion = region?.toUpperCase();

  // getting query data
  const { loading, error, data } = useQuery<BannerInterface>(BANNER_QUERY, {
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
      country_Code: defaultRegion,
      language_Code: locale?.toLocaleUpperCase(),
      date: DateTime.today(),
    },
    context: {
      clientName: "webstore-with-cdn",
    },
    skip: !Boolean(defaultRegion),
  });

  const bannerData = data?.banners;

  // setup for current direction
  const direction = getDirection(locale);

  const [bannerContent, setBannerContent] = useState<string>("");

  // popup status state
  const [popUpOpen, setPopUpOpen] = useState(false);

  // material ui theme
  const theme = useTheme();

  // material UI mediaQuery
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const bannerClick = (
    isPopup: boolean,
    bannerDescription: string,
    url: string,
    event: React.MouseEvent<HTMLElement>
  ) => {
    if (isPopup) {
      setPopUpOpen(true);
      setBannerContent(bannerDescription);
    } else {
      setBannerContent("");
      setPopUpOpen(false);
      router.push(url, undefined);
    }
  };

  /**
   * @method handlePopOverClose : Popup Closing
   * @description To set anchor element null on popover close
   */
  const handlePopUpClose = () => {
    setPopUpOpen(false);
  };

  const filterBannerData = (bannerType: string): BannerItems[] | undefined => {
    if (bannerData) {
      return bannerData.filter(
        ({ bannerCategory: { code } }) => code === bannerType
      );
    }
  };

  // Main banner data
  const mainBanner = filterBannerData(BANNER_TYPE.MAIN);

  // side banner data
  const sideBanner = filterBannerData(BANNER_TYPE.SIDE);

  // is sliding
  const isSliding = mainBanner && mainBanner[0]?.bannerCategory?.isSliding;

  // sliding interval
  const slidingTimeInterval =
    mainBanner && mainBanner[0]?.bannerCategory?.slidingTimeInterval;

  // main banner auto play config
  const mainBannerSwiperConfig = {
    delay: !!slidingTimeInterval ? slidingTimeInterval * 1000 : 3000,
    disableOnInteraction: false,
  };

  // swiper configs
  const swiperConfig = isSliding ? mainBannerSwiperConfig : isSliding;

  return (
    <>
      {loading && <BannerSkeleton />}
      {data && (
        <>
          <style jsx>{`
            .banner :global(.swiper-button-next:hover::after),
            .banner :global(.swiper-button-prev:hover::after) {
              background: url("${imageBaseUrl}/icons/banner-arrow.svg")
                no-repeat center center;
            }

            .banner :global(.swiper-button-prev:hover::after) {
              transform: rotate(-180deg);
            }

            .banner :global(.swiper-container) {
              height: 100%;
            }
          `}</style>
          {!!mainBanner?.length || sideBanner ? (
            <div
              className={`banner ${styles.banner}`}
              id="e2eTestingMainBannerWrapper"
              data-testid="homeBanner"
            >
              <div className={`container ${styles["banner__wrapper"]}`}>
                {!!mainBanner?.length ? (
                  <div className={styles["banner__slider-panel"]}>
                    <Swiper
                      // install Swiper modules
                      modules={[Autoplay, Navigation]}
                      dir={direction}
                      className="rounded"
                      spaceBetween={50}
                      slidesPerView={1}
                      navigation
                      autoplay={swiperConfig}
                      lazy={true}
                    >
                      {mainBanner?.map(
                        (
                          { image, url, bannerDescription, isPopup },
                          index: number
                        ) => (
                          <SwiperSlide key={index}>
                            <a
                              id="e2eTestingMainBannerURL"
                              className={styles["banner-link"]}
                              onClick={(event) =>
                                bannerClick(
                                  isPopup,
                                  bannerDescription,
                                  url,
                                  event
                                )
                              }
                              data-testid="mainBannerLink"
                            >
                              <div className={styles["main-banner-image"]}>
                                {image ? (
                                  <Image
                                    blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                                    placeholder="blur"
                                    src={image}
                                    alt="banner"
                                    width={832}
                                    height={400}
                                    priority
                                    className={styles["banner-image"]}
                                    id="e2eTestingMainBannerImageWithURL"
                                    data-testid="mainBannerImage"
                                    unoptimized={true}
                                  />
                                ) : (
                                  ""
                                )}
                              </div>
                            </a>
                          </SwiperSlide>
                        )
                      )}
                    </Swiper>
                  </div>
                ) : (
                  ""
                )}
                {!!sideBanner ? (
                  <div className={`${styles["banner__image-panel"]} md-hide`}>
                    {sideBanner[0]?.image ? (
                      <Link legacyBehavior href={sideBanner[0]?.url}>
                        <a
                          className="rounded"
                          id="e2eTestingsideBannerImageURL"
                          data-testid="sideBannerLink"
                        >
                          <Image
                            blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                            placeholder="blur"
                            src={sideBanner[0]?.image}
                            alt="banner"
                            id="e2eTestingsideBannerImage"
                            data-testid="sideBannerImage"
                            className={`${styles["banner__image"]}`}
                            width={428}
                            height={400}
                            priority
                            unoptimized={true}
                          />
                        </a>
                      </Link>
                    ) : (
                      ""
                    )}
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          ) : (
            ""
          )}
          <Dialog
            fullScreen={fullScreen}
            open={popUpOpen}
            onClose={handlePopUpClose}
            className="rounded-22"
            aria-labelledby="download"
            id="e2eTestingDialog"
            data-testid="bannerDialogWrapper"
          >
            <a
              className={`icon-close ${styles["icon-close"]}`}
              data-testid="downloadPopuClose"
              onClick={handlePopUpClose}
            ></a>
            <div
              data-testid="bannerDialog"
              dangerouslySetInnerHTML={{ __html: bannerContent }}
              className={styles["banner-content"]}
              id="e2eTestingBannerPopover"
            ></div>
          </Dialog>
        </>
      )}
    </>
  );
};

export default Banner;
