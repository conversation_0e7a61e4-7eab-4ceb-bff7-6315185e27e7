import React, { useEffect, useRef, useState } from 'react';
import styles from './marketingBanners.module.scss'
import ImageSlider from '@features/common/imageSlider/ImageSlider';
import { homeBannerDataQuery } from '../homeAPI';
import useAppRouter from '@features/common/router.context';
import { BANNER_TYPE } from '@constants/common';
import getConfig from 'next/config';
import Link from 'next/link';
import MarketingBannerSkeleton from '@features/allBrands/contentLoader/marketingBannerSkeleton/MarketingBannerSkeleton';
import gsap from 'gsap';

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

interface Banner {
    imageWebp?: string;
    image?: string;
    altText?: string;
    hasButton?: boolean;
    url?: string;
    buttonText?: string;
}

const MarketingBanners = () => {
    const {
        state: { region, locale },
    } = useAppRouter();

    const bannerRef = useRef(null);

    const [sliderImages, setSliderImages] = useState<Banner[]>([]);
    const [mainBanner, setMainBanner] = useState<Banner | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (region && locale) {
            getData();
        }
    }, [region, locale]);

    useEffect(() => {
        if (!loading) {
            const banner = bannerRef.current;

            // Initial rotation along X-axis
            gsap.set(banner, {
                transformOrigin: 'top center',
                rotationX: -30,
            });

            // Smooth swing back to 0°
            gsap.to(banner, {
                rotationX: 0,
                duration: 2.2, // a bit slower
                ease: 'power4.out', // smooth and decelerating
            });
        }
    }, [loading]);

    const getData = async () => {
        const mainBannerQuery = homeBannerDataQuery(region, locale, BANNER_TYPE.MAIN);
        const sideBannerQuery = homeBannerDataQuery(region, locale, BANNER_TYPE.SIDE)
        const [mainBannerData, sideBannerData] = await Promise.all([mainBannerQuery, sideBannerQuery]);
        setSliderImages(sideBannerData?.data?.banners || []);
        setMainBanner(mainBannerData?.data?.banners?.[0] || null);
        setLoading(false);
    }

    return (
        <>
            {loading && <MarketingBannerSkeleton />}
            {!loading && (
                <div className={`container ${styles['marketing-banners']}`} >
                    <div className={`${styles['single-banner']}`} ref={bannerRef} style={{ perspective: 1000 }} >
                        {
                            <>
                                {mainBanner && <img 
                                    src={mainBanner?.imageWebp || mainBanner?.image}
                                    alt="bannerImage"
                                />}

                                {mainBanner?.['hasButton'] &&
                                    <Link legacyBehavior href={`${mainBanner?.['url']}`}>
                                        <a>
                                            <div className={`${styles['single-banner__button']}`}>
                                                <span>
                                                    {mainBanner?.['buttonText']}
                                                </span>
                                                <img src={`${imageBaseUrl}/images/line-arrow-right.svg`} />
                                            </div>
                                        </a>
                                    </Link>
                                }
                            </>

                        }
                    </div>
                    {sliderImages && sliderImages.length > 0 && sliderImages?.[0]?.imageWebp && <ImageSlider images={sliderImages} />}
                </div>
            )}

        </>

    );
};

export default MarketingBanners;