import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
// import Swiper core and required modules
import { Autoplay, Navigation, Mousewheel } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import styles from "./SliderBanner.module.scss";
import getConfig from "next/config";
import getDirection from "@utils/getDirection";
import { BannerItems } from "@interfaces/common.inteface";
import { Dialog, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/system";
import { BANNER_TYPE, PLATFORM_TYPE } from "@constants/common";
import DateTime from "@utils/dateTime";
import { useQuery } from "@apollo/client";
import { BannerInterface } from "@features/home/<USER>/banner.interface";
import SliderBannerSkeleton from "../contentLoader/sliderBannerSkeleton/SliderBannerSkeleton";
import { BANNER_QUERY } from "./sliderBanner.query";
import useAppRouter from "@features/common/router.context";
import isAbsoluteURL from "@utils/isAbsoluteURL";
import { extractEmbeddedId } from "@utils/regex";
import { supportWebP } from "@utils/detectWebp";



// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method SliderBanner
 * @description banner wrapper component
 * @returns {JSX.Element}
 */

const SliderBanner = (): JSX.Element => {
  // getting locale
  const {
    state: { region, locale },
    router,
  } = useAppRouter();

  const [webpSupported, setWebpSupported] = useState<boolean>(false);

  // Youtube Base URL
  const YoutubeBaseUrl = `https://www.youtube.com/embed/`;

  const defaultRegion = region?.toUpperCase();

  // getting query data
  const { loading, error, data } = useQuery<BannerInterface>(BANNER_QUERY, {
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
      country_Code: defaultRegion,
      language_Code: locale?.toLocaleUpperCase(),
      bannerCategory_Code: BANNER_TYPE.MAIN,
      date: DateTime.today(),
    },
    context: {
      clientName: "webstore-with-cdn",
    },
    skip: !Boolean(defaultRegion),
  });

  const bannerData = data?.banners || [];

  // setup for current direction
  const direction = getDirection(locale);

  const [bannerContent, setBannerContent] = useState<string>("");

  const [isVideo, setIsVideo] = useState<any>();

  // popup status state
  const [popUpOpen, setPopUpOpen] = useState(false);

  // material ui theme
  const theme = useTheme();

  // material UI mediaQuery
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));

  const bannerClick = (
    isPopup: boolean,
    bannerDescription: string,
    url: string,
    isVideo: boolean,
    event: React.MouseEvent<HTMLElement>
  ) => {
    if (isPopup) {
      setIsVideo(isVideo);
      let bannerData = extractUrl(isVideo, url);
      setBannerContent(bannerData);
      setPopUpOpen(true);
    } else {
      setBannerContent("");
      setPopUpOpen(false);

      if (isAbsoluteURL(url)) {
        location.href = url;
      } else {
        router.push(url);
      }
    }
  };

  const extractUrl = (isVideo: boolean, url: string) => {
    if (isVideo) {
      // Extract the video ID using the regular expression
      const match = url?.match(extractEmbeddedId);
      return match && match[1] ? match[1] : url || "";
    } else {
      return url;
    }
  };

  /**
   * @method handlePopOverClose : Popup Closing
   * @description To set anchor element null on popover close
   */
  const handlePopUpClose = () => {
    setPopUpOpen(false);
  };

  const filterBannerData = (bannerType: string): BannerItems[] | undefined => {
    if (bannerData) {
      return bannerData.filter(
        ({ bannerCategory: { code } }) => code === bannerType
      );
    }
  };

  // Main banner data
  const mainBanner = filterBannerData(BANNER_TYPE.MAIN);

  // side banner data
  const sideBanner = filterBannerData(BANNER_TYPE.SIDE);

  // is sliding
  const isSliding = mainBanner && mainBanner[0]?.bannerCategory?.isSliding;

  // sliding interval
  const slidingTimeInterval =
    mainBanner && mainBanner[0]?.bannerCategory?.slidingTimeInterval;

  // main banner auto play config
  const mainBannerSwiperConfig = {
    delay: !!slidingTimeInterval ? slidingTimeInterval * 1000 : 3000,
    disableOnInteraction: false,
  };

  // swiper configs
  const swiperConfig = isSliding ? mainBannerSwiperConfig : isSliding;

  useEffect(() => {
    supportWebP().then((result: any) => setWebpSupported(result));
  }, []);

  return (
    <>
      {loading && <SliderBannerSkeleton />}
      {bannerData?.length > 0 && (
        <>
          <style jsx>{`
            .banner :global(.swiper-button-next),
            .banner :global(.swiper-button-prev) {
              width: 50px;
              height: 50px;
              background-color: #000;
              border-radius: 50%;
            }

            .banner :global(.swiper-container) {
              height: 100%;
              overflow: visible;
              position: unset;
            }
          `}</style>
          {!!mainBanner?.length || sideBanner ? (
            <div
              className={`banner ${styles.banner}`}
              id="e2eTestingMainBannerWrapper"
              data-testid="homeBanner"
            >
              <div className={`${styles["banner__wrapper"]} container`}>
                {!!mainBanner?.length ? (
                  <div className={styles["banner__slider-panel"]}>
                    <Swiper
                      // install Swiper modules
                      modules={[Autoplay, Navigation, Mousewheel]}
                      dir={direction}
                      className="rounded"
                      allowTouchMove={true}
                      breakpoints={{
                        1320: {
                          slidesPerView: 3.3,
                          spaceBetween: 26,
                        },
                        1064: {
                          slidesPerView: 2.7,
                          spaceBetween: 26,
                        },
                        760: {
                          slidesPerView: 2,
                          spaceBetween: 26,
                        },
                        480: {
                          slidesPerView: 2,
                          spaceBetween: 16,
                        },
                      }}
                      navigation
                      mousewheel
                    >
                      {mainBanner?.map(
                        (
                          {
                            image,
                            url,
                            bannerDescription,
                            isPopup,
                            isVideo,
                            borderColor,
                            imageWebp,
                          },
                          index: number
                        ) => (
                          <SwiperSlide key={index}>
                            <a
                              id="e2eTestingMainBannerURL"
                              className={styles["banner-link"]}
                              onClick={(event) =>
                                bannerClick(
                                  isPopup,
                                  bannerDescription,
                                  url,
                                  isVideo,
                                  event
                                )
                              }
                              data-testid="mainBannerLink"
                            >
                              <div
                                className={styles["main-banner-image"]}
                                style={{ border: `1px solid ${borderColor}` }}
                              >
                                {image ? (
                                  <Image
                                    blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                                    placeholder="blur"
                                    src={(webpSupported ? imageWebp : image) || image}
                                    alt="banner"
                                    layout="fill"
                                    priority
                                    className={styles["banner-image"]}
                                    id="e2eTestingMainBannerImageWithURL"
                                    data-testid="mainBannerImage"
                                    unoptimized={true}
                                  />
                                ) : (
                                  ""
                                )}
                              </div>
                            </a>
                          </SwiperSlide>
                        )
                      )}
                    </Swiper>
                  </div>
                ) : (
                  ""
                )}
                {!!sideBanner ? (
                  <div className={`${styles["banner__image-panel"]} md-hide`}>
                    {sideBanner[0]?.image ? (
                      <Link legacyBehavior href={sideBanner[0]?.url}>
                        <a
                          className="rounded"
                          id="e2eTestingsideBannerImageURL"
                          data-testid="sideBannerLink"
                          onClick={() => {}}
                        >
                          <Image
                            blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                            placeholder="blur"
                            src={sideBanner[0]?.image}
                            alt="banner"
                            id="e2eTestingsideBannerImage"
                            data-testid="sideBannerImage"
                            className={`${styles["banner__image"]}`}
                            width={428}
                            height={400}
                            priority
                            unoptimized={true}
                          />
                        </a>
                      </Link>
                    ) : (
                      ""
                    )}
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          ) : (
            ""
          )}
          <Dialog
            fullScreen={fullScreen}
            open={popUpOpen}
            onClose={handlePopUpClose}
            className="rounded-22"
            aria-labelledby="download"
            id="e2eTestingDialog"
            data-testid="bannerDialogWrapper"
          >
            <a
              className={`icon-close ${styles["icon-close"]}`}
              onClick={handlePopUpClose}
            ></a>
            {isVideo ? (
              <iframe
                width="600"
                height="480"
                allowFullScreen
                className={`${styles["youtube-iframe"]}`}
                src={`${YoutubeBaseUrl}${bannerContent}`}
                id="e2eTestingTestimonialYTPlayerIframe"
                data-testid="ytPopupIframe"
              ></iframe>
            ) : (
              <img
                className={`${styles["image-banner"]}`}
                src={bannerContent}
              />
            )}
          </Dialog>
        </>
      )}
    </>
  );
};

export default SliderBanner;
