import React from "react";
import styles from "./SolutionHubBanner.module.scss";
import getConfig from "next/config";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useMediaQuery } from "@mui/material";
import useAppRouter from "@features/common/router.context";

const SolutionHUbBanner = () => {
  // taking public image config url
  const {
    publicRuntimeConfig: { imageBaseUrl, solutionsHubURL },
  } = getConfig();

  // #. Get translations
  const { t } = useTranslation("common");

  // #. Get Locale
  const {
    state: { locale },
  } = useAppRouter();

  const backgroundImage = `${imageBaseUrl}/images/solutionBg.png`;
  const backgroundImageMax = `${imageBaseUrl}/images/solutionBgMax.png`;
  const backgroundImageArabic = `${imageBaseUrl}/images/solutionBgArabic.png`;
  const backgroundImageMaxArabic = `${imageBaseUrl}/images/solutionBgMaxArabic.png`;
  const icon = `${imageBaseUrl}/icons/rocket.svg`;

  // #. Set if web using media query
  const isWeb = useMediaQuery("(max-width:1800px)");
  return (
    <>
      <style jsx>{`
        .bgImage {
          background: url("${locale === "ar"
            ? backgroundImageArabic
            : backgroundImage}");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          background-color: #270c27;
        }
      `}</style>

      <style jsx>{`
        .bgImageMax {
          background: url("${locale === "ar"
            ? backgroundImageMaxArabic
            : backgroundImageMax}");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          background-color: #270c27;
        }
      `}</style>

      <div
        className={
          isWeb
            ? `bgImage ${styles["banner"]}`
            : `bgImageMax ${styles["banner"]}`
        }
      >
        <div className={styles["banner__contents"]}>
          <h6>{t("behindScenes")}</h6>
          <span>
            <img
              src={`${imageBaseUrl}/images/union.svg`}
              width={64}
              height={64}
            />
            <h5>{t("solutionHub")}</h5>
          </span>

          <p>{t("totalGift")}</p>
          <p>{t("powering")}</p>
        </div>
        <Link legacyBehavior href={`${solutionsHubURL}${locale}`} passHref>
          <a>
            <div className={styles["banner__button"]}>
            <img src={icon} alt="icon" width={24} height={24} />
            {t("expoloreHub")}
          </div>
          </a>
          
        </Link>
      </div>
    </>
  );
};

export default SolutionHUbBanner;
