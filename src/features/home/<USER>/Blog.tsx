import Button from "@features/common/button/Button";
import React, { useEffect, useState } from "react";
import { useTranslation } from "next-i18next";
import { Autoplay, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import styles from "./Blog.module.scss";
import getDirection from "@utils/getDirection";
import { BlogsInterface } from "@features/home/<USER>/blogs.interface";
import { useQuery } from "@apollo/client";
import { BLOG_QUERY } from "./blog.query";
import BlogSkeleton from "../contentLoader/blogSkeleton/BlogSkeleton";
import useAppRouter from "@features/common/router.context";
import isAbsoluteURL from "@utils/isAbsoluteURL";
import { supportWebP } from "@utils/detectWebp";
import Image from "next/image";
import getConfig from "next/config";



// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method Blog
 * @description Blog elements ( swiper slider used for slide) component
 * @param blogData : blog data items
 * @returns {JSX.Element}
 */

const Blog = () => {
  // translations
  const { t } = useTranslation("common");
  const [webpSupported, setWebpSupported] = useState<boolean>(false);

  // getting locale
  const {
    state: { region, locale },
    router,
  } = useAppRouter();

  // getting query data
  const { loading, error, data } = useQuery<BlogsInterface>(BLOG_QUERY, {
    context: {
      clientName: "webstore-with-cdn",
    },
    skip: !Boolean(region),
  });

  const blogData = data?.blogs.edges;

  // setup for current direction
  const direction = getDirection(locale);

  /**
   * Button Action
   */
  const blogAction = (linkURL: string) => {
    if (isAbsoluteURL(linkURL)) {
      location.href = linkURL;
    } else {
      router.push(linkURL);
    }
  };

  useEffect(() => {
    supportWebP().then((result: any) => setWebpSupported(result));
  }, []);

  return (
    <>
      {loading && <BlogSkeleton />}
      {data && (
        <div className={`blog ${styles.blog}`} id="e2eTestingBlogWrapper">
          <div className={`container ${styles["blog__container"]} rounded`}>
            <Swiper
              // install Swiper modules
              modules={[Autoplay, Pagination]}
              dir={direction}
              className="rounded"
              pagination={{ clickable: true }}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
              }}
            >
              {blogData?.map(
                ({ node: { image, title, url, imageWebp } }, index) => (
                  <SwiperSlide
                    className={styles["swiper-slide-wrapper"]}
                    key={index}
                    data-testid="blogWrapper"
                  >
                    <div className={styles["blog__image"]}>
                      <Image
                        //@ts-ignore
                        src={
                          webpSupported && imageWebp
                            ? imageWebp
                            : image ||
                              `${imageBaseUrl}/images/preload-image.jpeg`
                        }
                        alt={title}
                        height={360}
                        width={640}
                        unoptimized={true}
                        blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                        id={`e2eTestingBlogImage${index}`}
                        data-testid="blogImage"
                        placeholder="blur"
                      />
                    </div>
                    <div className={styles["blog__wrapper"]}>
                    <div
                      className={styles["blog__content"]}
                      id={`e2eTestingBlogContent${index}`}
                    >
                      <span className={styles["blog__category"]}>
                        {t("blog")}
                      </span>
                      <h2
                        className={styles["blog__title"]}
                        data-testid="blogTitle"
                      >
                        {title}
                      </h2>

                      
          <a className={styles["read-more"]} onClick={() => blogAction(url)}>
          {t("readMore")}
            <span className={`${styles["icon-wrapper"]} button-icon-wrapper`}>
              <i className={`icon-arrow-forward`}></i>
            </span>
          </a>
        

                      {/* <Button
                        action={() => blogAction(url)}
                        className={styles["blog__button"]}
                        theme="secondary"
                        arrow="arrow-forward-black"
                        id={`e2eTestingBlogReadMoreButton${index}`}
                      >
                        {t("readMore")}
                      </Button> */}
                    </div>
                    </div>
                  </SwiperSlide>
                )
              )}
            </Swiper>
          </div>
        </div>
      )}
    </>
  );
};

export default Blog;
