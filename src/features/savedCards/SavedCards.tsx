import React, { useEffect, useState } from "react";
import EmptySavedCards from "./emptySavedCards/EmptySavedCards";
import SavedCardList from "./savedCardList/SavedCardList";
import { useTranslation } from "next-i18next";
import styles from "./SavedCards.module.scss";
import useOrdersAPI from "@features/orders/ordersAPI";
import { getTokenInfo } from "@features/common/commonSlice";
import { useAppSelector } from "@redux/hooks";
import useAppRouter from "@features/common/router.context";
import SavedCardsContentLoader from "./contentLoader/SavedCardsContentLoader";

const SavedCards = () => {
  //getting activeRegion
  const {
    state: {activeRegion,},
  } = useAppRouter();  

  // #. Get translations
  const { t } = useTranslation("common");
  const tokenInfo = useAppSelector(getTokenInfo);
  
  const { FetchSavedCardsData } = useOrdersAPI();

  const [savedCardsData, setSavedCardsData] =useState<any>()

  const { data } = savedCardsData || {}
  const loading = !data?.savedCards?.length && !data?.savedCards

  const {refetchSavedCards, savedCardsLoading} = FetchSavedCardsData(
    tokenInfo?.AccessToken,
    activeRegion?.node?.code
  );

  const fetchSavedCards = () =>{
    refetchSavedCards().then((data)=>{
      setSavedCardsData(data)
     })
  }

  useEffect(() => {
    if (activeRegion?.node?.code) {
      fetchSavedCards()
    }
  }, [activeRegion])

  return (data?.savedCards?.length) ? (
    <>
      <h2 className={styles["saved-card-title"]}>{t("yourCards")}</h2>
      {data?.savedCards?.map((item: any, index: number) => (
        <SavedCardList token={tokenInfo?.AccessToken} key={index} {...item} fetchSavedCards={fetchSavedCards}/>
      ))}
    </>
  ) : (loading) ? (
    <SavedCardsContentLoader/>
  ) :(
    <EmptySavedCards />
  );
};

export default SavedCards;
