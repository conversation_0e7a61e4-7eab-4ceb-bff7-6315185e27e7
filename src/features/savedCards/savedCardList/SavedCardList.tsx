import React, { useState } from "react";
import styles from "./SavedCardList.module.scss";
import getConfig from "next/config";
import Confirm from "@features/common/confirm/Confirm";
import { useTranslation } from "next-i18next";
import useOrdersAPI from "@features/orders/ordersAPI";
import useAppRouter from "@features/common/router.context";
import { useAppDispatch } from "@redux/hooks";
import { setNotifierState } from "@features/common/commonSlice";

interface SavedCardListInterface {
  cardLast4: string;
  cardExpiry: string;
  customerName: string;
  id: string;
  token: string;
  paymentScheme: string;
  fetchSavedCards: () => void;
}

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const SavedCardList = ({
  cardLast4,
  cardExpiry,
  customerName,
  id,
  token,
  paymentScheme,
  fetchSavedCards
}: SavedCardListInterface) => {
  // #. Get translations
  const { t } = useTranslation("common");

  //getting activeRegion
  const {
    state: { activeRegion },
  } = useAppRouter();

  const { removeSavedCard } = useOrdersAPI();
  // #. Notification dispatch
  const notifierDispatch = useAppDispatch();

  const cardType = `${imageBaseUrl}/icons/cards/${paymentScheme.toLowerCase()}.webp` 

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);

  const maskText = (lastFour: string): string => {
    return "XXXX - XXXX - XXXX - " + lastFour;
  };

  const onRemoveCard = () => {
    setOpenConfirm(true);
  };

  const onSuccess = (responses: any) => {
    if(responses?.data?.deleteSavedCard){
    const { success= false , message = "" } = responses?.data?.deleteSavedCard || {}
    const icon = success ?  "checkOutline" : "errorOutline"
    notifierDispatch(
      setNotifierState({
        title: "",
        description: message,
        icon: icon,
      })
    );
    fetchSavedCards()
  }
  };

  const onError = (error: any) => {
    const errorMessage = error?.message;
    notifierDispatch(
      setNotifierState({
        title: "",
        description: errorMessage,
        icon: "errorOutline",
      })
    );
  };

  const onConfirmClose = (doProcess: boolean) => {
    if (doProcess) {
      removeSavedCard({
        id,
        regionCode: activeRegion?.node?.code,
        token,
        onSuccess,
        onError,
      });
    }
    setOpenConfirm(false);
  };

  return (
    <>
      <div className={styles["saved-cards"]}>
        <img
          className={styles["saved-cards__card-image"]}
          src={cardType}
        />
        <div className={styles["saved-cards__details-container"]}>
          <p className={styles["saved-cards__card-num"]}>
            {maskText(cardLast4)}
          </p>
          <p className={styles["saved-cards__card-details"]}>
            {customerName} {"•"} {cardExpiry}
          </p>
        </div>
        <div>
          <img
            onClick={onRemoveCard}
            className={styles["saved-cards__delet-icon"]}
            src={`${imageBaseUrl}/icons/deleteSvg.svg`}
          />
        </div>
      </div>

      <Confirm
        className="remove-cards-confirm"
        title={t("deleteCard") || ""}
        message={t("cardRemoveConfirm")}
        open={openConfirm}
        onClose={onConfirmClose}
        confirmText={t("submit")}
      />
    </>
  );
};

export default SavedCardList;
