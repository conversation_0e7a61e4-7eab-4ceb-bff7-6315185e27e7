@import "@styles/variables";
@import "@styles/mixins";

.saved-cards {
  padding: 16px 0;
  border-bottom: 1px solid $semi-dark-grey1;
  display: flex;
  align-items: center;
  gap: 24px;

  &:last-child {
    margin-bottom: 40px;
  }

  &__details-container {
    flex: 1;
  }

  &__card-image {
    height: 16px;
  }

  &__card-num {
    font-size: 16px;
    font-weight: 700;
    color: $dark-purple;
    margin: 0;
    margin-bottom: 8px;
  }

  &__card-details {
    margin: 0;
    color: $dark-purple;
    font-size: 12px;
    font-weight: 500;
  }

  &__delet-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
