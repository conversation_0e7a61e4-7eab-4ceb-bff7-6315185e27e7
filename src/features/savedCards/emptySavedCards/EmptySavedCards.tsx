import { useTranslation } from "next-i18next";
import React from "react";
import styles from "./EmptySavedCards.module.scss";
import getConfig from "next/config";

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const EmptySavedCards = () => {
  // #. Get translations
  const { t } = useTranslation("common");
  return (
    <div className={styles["empty-cards"]}>
      <img src={`${imageBaseUrl}/icons/empty-page.svg`} />
      <h2>{t("noSavedCards")}</h2>
    </div>
  );
};

export default EmptySavedCards;
