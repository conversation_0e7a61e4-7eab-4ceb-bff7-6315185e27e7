import { gql } from "@apollo/client";

export const DEFAULT_STORE_BRAND_QUERY = gql`
query DefaultStoreBrand($countryCode: String!) {
    defaultStoreBrand(countryCode: $countryCode) {
      __typename
      ... on BrandNode {
        code
        primaryCategory {
          code
          name
          nameEn
        }
        primaryCategoryForEvent {
          code
          name
          nameEn
        }
        brandImageData
        name
        nameEn
        id
        slug
        offerPageSiteMeta {
          title
          description
          keywords
          urlPattern
          noIndex
        }
        logoImageData
        description
        redemptionType
        redemptionTypeForEvent
        redemptionDetails
        defaultGenericOccasion {
          edges{
            node{
              occasion {
                name
                code
              }
            }
          }
        }
        label
        requireMobileVerification
      }
      ... on ErrorNode {
        message
        status
      }
    }
  }`;

export const DEFAULT_STORE_BRAND_INSTANCE_QUERY = gql`
query DefaultStoreBrand($countryCode: String!) {
  defaultStoreBrand(countryCode: $countryCode) {
      __typename
      ... on BrandNode {
        isOffer
        expiry
        isPrintathomeEnabled
        denominationRange
        variableDenomination
        minDenominationAmount
        maxDenominationAmount
        currencyDecimalNotation
        renderPreviewPage
        buyForYourself
        currency {
          name
          code
        }
        storeLocations {
          edges {
            node {
              code
              contactNumber
              address
              city {
                code
                name
              }
            }
          }
        }
        redemptionBadges {
          label
          type
        }
        imageGallery {
          edges {
            node {
              image
              caption
            }
          }
        }
      }
      ... on ErrorNode {
        status
        message
      }
    }
  }
`;

export const DEFAULT_BRAND_OFFERS = gql`
query getDefaultBrandOffersV2($countryCode: String!) {
  defaultStoreBrandOffers(countryCode: $countryCode) {
    edges {
      node {
        offerHeader
        offerText
        offerTerms
        applicableAmountText
        validityText
        disableWarningText
        offerType
        isDisabled
        brand {
          name
          brandImageData
          redemptionDetails
          description
          logoImageData
          slug
          code
        }
        offerDetailPageSiteMeta{
          title
          description
          keywords
          noIndex
        }
      }
    }
  }
}
`;

export const DEFAULT_BRAND_PROMOS = gql`
query getDefaultBrandPromoCodesV2($countryCode: String!) {
  defaultStoreBrandPromoCodes(countryCode: $countryCode) {
    edges {
      node {
        brand {
          name
          logoImageData
          brandImageData
          slug
          code
          redemptionDetails
          description
        }
        offerDetailPageSiteMeta{
          title
          description
          keywords
          noIndex
        }
        promoLabel
      }
    }
  }
}
`;

export const PROMO_QUERY = gql`
  query offersAndPromoCodes($brandCode: String!, $genericBrandCode: String!, $offerType: OfferType) {
    offersAndPromoCodes(brandCode: $brandCode, genericBrandCode: $genericBrandCode, offerType: $offerType) {
      totalCount
      edges {
        node {
          disableWarningText
          offerHeader
          offerText
          offerTerms
          applicableAmountText
          validityText
          offerType
          brand {
            name
            brandImageData
            code
          }
        }
      }
    }
  }
`;