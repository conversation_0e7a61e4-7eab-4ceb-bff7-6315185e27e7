import Link from 'next/link';
import styles from './OffersBrandQuickInfo.module.scss';
import Image from "next/image";
import getConfig from 'next/config';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const OffersBrandQuickInfo = ({
    brandImageData,
    expiry,
    redemptionBadges
}: any) => {

    
    // #. preload image
    const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
    return (
        <div className={styles['quickInfo-container']}>
            <Link legacyBehavior href="#">
                <a>
                    <Image
                        blurDataURL={preloadImage}
                        placeholder="blur"
                        src={brandImageData || preloadImage}
                        alt={"name"}
                        width={416}
                        height={266}
                        unoptimized={true}
                        layout={"responsive"}
                        data-testid="quickInfoSingleImage"
                    />
                </a>
            </Link>
            <div className={styles['info']}>
                <span className={styles['validity']}>
                    {expiry}
                </span>
                <div className={styles['tags-container']}>
                    {
                     redemptionBadges?.map((badge: any, idx: number) => (
                        <span className={styles['tag']} key={badge.id || idx}>
                          {badge.label}
                        </span>
                      ))
                    }
                </div>
            </div>
        </div>
    )
}

export default OffersBrandQuickInfo;