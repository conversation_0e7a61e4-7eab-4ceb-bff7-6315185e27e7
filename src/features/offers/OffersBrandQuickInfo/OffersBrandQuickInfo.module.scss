@import "@styles/mixins";
@import "@styles/variables";

.quickInfo-container {
    width: 415px;

    margin: 0 auto;
    margin-bottom: 32px;

    img {
        border-radius: 16px;
    }

}

.info {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: anchor-center;

    .validity {
        color: $dark-charcoal;

        font-family: "Mona Sans";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        /* 142.857% */
        letter-spacing: -0.14px;
    }
}

.tags-container {
    display: flex;
    gap: 8px;


    .tag {
        padding: 0px 10px 0px 10px;
        width: 69px;
        height: 32px;
        border-radius: 6px;
        background: #FFF;
        color: $grey-text;

        font-family: "Mona Sans";
        font-size: 10px;
        font-style: normal;
        font-weight: 700;

        /* 200% */
        letter-spacing: -0.1px;
        text-transform: uppercase;
        text-align: center;
        line-height: 32px;
    }
}