import styles from './OffersPageRightSection.module.scss'
import OfferBrandHeader from "../OfferBrandHeader/OfferBrandHeader";
import OffersBrandQuickInfo from "../OffersBrandQuickInfo/OffersBrandQuickInfo";
import OfferBrandDenominationNBuy from "../OfferBrandDenominationNBuy/OfferBrandDenominationNBuy";
import OfferPrintPDF from "../OfferPrintPDF/OfferPrintPDF";
import BrandOrderContinue from "@features/brand/brandOrderType/BrandOrderContinue";
import OfferBrandDescription from "../OfferBrandDescription/OfferBrandDescription";

/**
 * @method OfferPageRightSection
 * @description Brand quick info
 * @returns
 */
const OffersPageRightSection = ({
  brand,
  brandInstance
}: any): JSX.Element => {

  return (
    <div className={styles['right-section']}>


      <div className={styles['brand-card-wrapper']}>
        <OfferBrandHeader {...brand} />
        <OffersBrandQuickInfo {...brand} {...brandInstance} />
      </div>

      <OfferBrandDenominationNBuy brand={brand} brandInstance={brandInstance} />

      {brandInstance?.isPrintathomeEnabled && <OfferPrintPDF />}

      <BrandOrderContinue
        onStockUpdate={() => { }}
        renderPreviewPage={brandInstance?.renderPreviewPage}
        isOffersBuyNow={true}
        brandInstance={brandInstance}
      />

      <OfferBrandDescription description={brand?.description} redemptionDetails={brand?.redemptionDetails} slug={brand?.slug} />
    </div>
  );
};

export default OffersPageRightSection;
