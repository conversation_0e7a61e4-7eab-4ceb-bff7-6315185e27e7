import BrandHowToUseAccordion from "@features/brand/brandDescription/BrandHowToUseAccordion";
import styles from './OfferBrandDescription.module.scss';
import BrandAboutThisGiftAccordion from "@features/brand/brandDescription/BrandAboutThisGiftAccordion";
import BrandRedemptionDetailsAccordion from "@features/brand/brandDescription/BrandRedemptionDetailsAccordion";
import { useState } from "react";
import BrandRedeemablesAccordion from "@features/brand/brandDescription/BrandRedeemablesAccordion";
import { useTranslation } from "next-i18next";

const OfferBrandDescription = ({ description, redemptionDetails, slug }: any) => {
    // #. Get translations
    const { t } = useTranslation("common");
    const [expanded, setExpanded] = useState<any>("");
    /**
     * 
     * @param panel 
     */
    const handleChange = (panel: string) => {
        setExpanded(panel);
    }
    return (
        <>
            <div className={`${styles['offer-brand-description-container']} offer-brand-right-accordian`}>
                <BrandRedeemablesAccordion 
                    handleChangeEmitter={handleChange}
                    expanded={expanded}
                    slug={slug}
                    hasOffer={false}
                    label={t("brandsIncluded")}
                />
                <BrandAboutThisGiftAccordion
                    handleChangeEmitter={handleChange}
                    expanded={expanded}
                    description={description}
                    label={t('aboutHappyYou')}
                />
                <BrandRedemptionDetailsAccordion
                    handleChangeEmitter={handleChange}
                    expanded={expanded}
                    redemptionDetails={redemptionDetails}
                    label={t('happyYouTerms')}
                />
                <BrandHowToUseAccordion
                    handleChangeEmitter={handleChange}
                    expanded={expanded}
                    isGeneric={true}
                />
            </div>

        </>
    )
}

export default OfferBrandDescription;