import getConfig from "next/config";
import useAppRouter from "@features/common/router.context";
import { IBrandOffersNode } from "@features/brand/interfaces/brandOffers.interface";
import styles from './OfferInfo.module.scss';
import Image from "next/image";
import BrandAboutThisGiftAccordion from "@features/brand/brandDescription/BrandAboutThisGiftAccordion";
import BrandRedemptionDetailsAccordion from "@features/brand/brandDescription/BrandRedemptionDetailsAccordion";
import { useState } from "react";
import { useTranslation } from "next-i18next";

interface IOfferDialog {
    offerData: IBrandOffersNode;
    brandImage: string;
    onClose: () => void;
    genericBrandCode: string;
}

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method BrandOfferDialog
 * @description Offer details dialog
 * @returns {JSX.Element}
 */
const OfferInfo = ({ onBackClick, isOpen, offerData, brandImage, cardName, isOffer, promoData }: any): JSX.Element => {

    const { t } = useTranslation("common");
    const errorIcon = `${imageBaseUrl}/icons/error-red.svg`;


    const OFFER_TYPE_PROMO = "PROMO_CODE";
    const [currentPromoIndex, setCurrentPromoIndex] = useState(0);

    // #. Get Locale
    const {
        state: { locale },
    } = useAppRouter();

    const [expanded, setExpanded] = useState<any>();

    const back = `${imageBaseUrl}/icons/arrow-circle-left.svg`;
    const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
    const sliderLeftArrow = `${imageBaseUrl}/images/arrow-left.svg`;
    const sliderRightArrow = `${imageBaseUrl}/images/arrow-right.svg`;

    /**
     * @method handleClose
     */
    const handleClose = () => {
        onBackClick && onBackClick();
    };

    const handleChange = (panel: string) => {
        setExpanded(panel);
    };

    const handleNextPromoCodeClick = () => {
        if (currentPromoIndex < promoData.length - 1) {
            setCurrentPromoIndex(currentPromoIndex + 1);
        }
    }

    const handlePrevPromoCodeClick = () => {
        if (currentPromoIndex > 0) {
            setCurrentPromoIndex(currentPromoIndex - 1)
        }
    }

    return (
        <div className={`${styles['offer-info']} offer-info`}>
            {/* Back button */}
            <div className={styles['back-btn-container']}>
                <Image onClick={handleClose} src={back} width={32} height={32} alt="Back" />
                <p className={styles['back-btn-container__text']}>{t('back')}</p>
            </div>

            {/* Promos slider */}
            {
                !isOffer && promoData?.length > 1 && <div className={styles['promo-slider-container']}>
                    <div className={styles['promo-slider-container__promo-slider']}>
                        <img src={sliderLeftArrow} className={`${currentPromoIndex == 0 ? styles['promo-slider-container__promo-slider--icon-inactive'] : styles['promo-slider-container__promo-slider--icon-cursor']} }`} onClick={handlePrevPromoCodeClick} />
                        <div className={styles['promo-slider-container__slider-text']}>
                            <span>{currentPromoIndex + 1}</span>
                            <span className={styles['promo-slider-container__slider-text--text-of']}>of</span>
                            <span> {promoData?.length} {t('promocodes')}</span>
                        </div>
                        <img src={sliderRightArrow} className={`${currentPromoIndex == promoData.length - 1 ? styles['promo-slider-container__promo-slider--icon-inactive'] : styles['promo-slider-container__promo-slider--icon-cursor']} `} onClick={handleNextPromoCodeClick} />
                    </div>
                </div>
            }
            <div className={styles["offer-container"]}>
                {/* Offer Titles */}
                <div className={styles['offer-info-title']}>
                    <h3 className={styles['offer-info-title__text']}>
                        {offerData?.brand?.name} {t('egiftcardoffer')}
                    </h3>
                    <h3 className={`${styles['offer-info-title__text']} ${styles['offer-info-title__text--red']}`}>
                        {isOffer ? offerData?.offerText : promoData[currentPromoIndex]?.node?.offerText}
                    </h3>
                    <p className={`${styles['offer-info-title__text']} ${styles['offer-info-title__text--third-text']}`}>
                        {isOffer ? offerData?.offerHeader : promoData[currentPromoIndex]?.node?.offerHeader}
                    </p>
                </div>

                {/* Offer Images */}
                <div className={styles['offer-info-images']}>
                    <div className={`${styles[
                        "offer-info-images__images"
                    ]} ${styles[
                    "offer-info-images__images--img1"
                    ]}`}>
                        <Image
                            blurDataURL={preloadImage}
                            placeholder="blur"
                            src={brandImage}
                            alt="image"
                            width={188}
                            height={120}
                            layout={"responsive"}
                            unoptimized={true}

                        />
                    </div>
                    <div className={`${styles[
                        "offer-info-images__images"
                    ]} ${styles[
                    "offer-info-images__images--img2"
                    ]}`}>
                        <Image
                            blurDataURL={preloadImage}
                            placeholder="blur"
                            src={offerData?.brand?.brandImageData || preloadImage}
                            alt="image"
                            width={188}
                            height={120}
                            layout={"responsive"}
                            unoptimized={true}

                        />
                    </div>
                </div>

                {/* Divider */}
                <div className={styles["divider-container"]}>
                    <div className={styles["divider-container__line"]}></div>
                </div>

                <div className={styles["amount-validity__container"]}>
                    {/* Validity */}
                    <div className={styles["validity"]}>
                        <p className={styles["amount-validity__title"]}>{t("expiresOn")}</p>
                        <p className={styles["amount-validity__text"]}>
                            {isOffer
                                ? offerData?.validityText
                                : promoData[currentPromoIndex]?.node?.validityText}
                        </p>
                    </div>
                    {/* Applicable amount */}
                    {(offerData?.applicableAmountText ||
                        promoData && promoData[currentPromoIndex]?.node?.applicableAmountText) &&
                        <div className={styles["applicable-amount"]}>
                            <p className={styles["amount-validity__title"]}>
                                {t("applicableAmounts")}
                            </p>
                            <p className={styles["amount-validity__text"]}>
                                {isOffer
                                    ? offerData?.applicableAmountText
                                    : promoData[currentPromoIndex]?.node?.applicableAmountText}
                            </p>
                        </div>
                    }
                </div>

                {/* Offer exhausted  */}
                {
                    offerData?.isDisabled &&
                    <div className={styles['offer-exhausted']}>
                        <div className={styles['offer-exhausted__content']}>
                            <img src={errorIcon} />
                            <span className={styles['offer-exhausted__content--text']}>
                                {offerData?.disableWarningText}
                            </span>
                        </div>
                    </div>
                }
                

                {/* How it works */}
                {
                    !offerData?.isDisabled &&
                    <div className={styles['how-it-works']}>
                        <span className={styles['how-it-works__title']}>{t("worksTitle")}</span>
                        <ol className={styles['how-it-works__list']} type="1">
                            <li>{t("worksText", { brandName: cardName })}</li>
                            <li>{t("spendText", {
                                brandName: offerData?.brand?.name,
                            })}</li>
                            <li>
                                {!isOffer ? t("promoText", { brandName: offerData?.brand?.name}) : t("valueText", {
                                    brandName: offerData?.brand?.name,
                                })}
                            </li>
                        </ol>
                    </div>
                }
                

                {/* Accordian */}
                <div className={`${styles['offer-brand-accordian']} offer-brand-accordian`}>
                    <BrandRedemptionDetailsAccordion
                        handleChangeEmitter={handleChange}
                        expanded={expanded}
                        redemptionDetails={offerData?.brand?.redemptionDetails}
                        label={t("termsAndCondition",{brandName:offerData?.brand?.name})}
                    />
                    <BrandAboutThisGiftAccordion
                        handleChangeEmitter={handleChange}
                        expanded={expanded}
                        description={offerData?.brand?.description}
                        label={t("aboutBrandCard",{brandName:offerData?.brand?.name})}
                    />
                </div>
            </div>
        </div>
    );
};

export default OfferInfo;
