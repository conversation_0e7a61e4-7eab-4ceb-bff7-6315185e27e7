@import "@styles/variables";
@import "@styles/mixins";


.offer-info{
    position: relative;
    width: 100%;
}

.offer-container{
    max-width: 496px;
    margin: auto;
}

.promo-slider-container {
    display: flex;
    justify-content: center;

    &__promo-slider {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
        margin-top: 32px;
        padding: 8px 4px;
        align-items: center;
        gap: 24px;
        border-radius: 0px 0px 8px 8px;
        background: $grey-bg;
        width: 259px;
        height: 40px;

        &--icon-inactive{
            opacity: 0.2;
        }

        &--icon-active{
            opacity: 1;
        }

        &--icon-cursor{
            cursor: pointer;
        }
    }

    &__slider-text {
        color: $dark-charcoal;
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;
        display: flex;
        width: 155px;
        justify-content: space-between;

        &--text-of {
            color: $grey-text;
            font-family: "Mona Sans";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 24px;
            letter-spacing: -0.16px;
        }
    }
}



.back-btn-container{
    display: flex;
    gap: 8px;
    &__text {
            color: $dark-charcoal;
            font-family: "Mona Sans";
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
            /* 21px */
    }

    @include rtl-styles{
        img{
            transform: rotate(180deg);
        }
    }
    
}

.offer-info-title{
    display: flex;
    flex-direction: column;
    align-items: center;

    &__text{
        color: $dark-charcoal;
        text-align: center;
        font-family: $bricolage-font-family;
        font-optical-sizing: none;
        font-size: 32px;
        font-style: normal;
        font-weight: 800;
        line-height: 48px; /* 150% */
        margin: 0px !important;

        &--red{
            color: $error-red !important;
        }

        &--third-text{
            font-family: "Mona Sans";
            font-size: 24px;
            line-height: 32px; /* 133.333% */
            letter-spacing: -0.24px;
            margin-top: 40px !important;
        }
    }
}

.offer-info-images{
    position: relative;
    width: 100%;
    height: 189px;
    margin-top: 32px;
    margin-bottom: 32px;

    &__images{
        width: 253.344px;
        height: 162.003px;
        position: absolute;
        border-radius: 9px;
        overflow: hidden;
        border: 1px solid rgba(92,99,105,.15);
        

        &--img1{
            top: 0px;
            left: 50%;
            transform: translate(-67%, 0px);
        }

        &--img2{
            top: 27px;
            left: 50%;
            transform: translate(-33%, 0px);
        }
    }
}

.divider-container {
    display: flex;
    justify-content: center;
    &__line {
        width: 496px;
        height: 0.5px;
        border: 0.5px dashed $divider-grey;
    }
}

.validity{
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    @media (max-width: 1320px) {
        margin-left: 5px;
    }

    &__title {
            color: $grey-text;
            /* Body/B3 */
            font-family: var(--Font-Family-body, "Mona Sans");
            font-size: var(--Font-Size-xs, 14px);
            font-style: normal;
            font-weight: 500;
            line-height: var(--Font-Line-height-sm, 16px);
            /* 114.286% */
            letter-spacing: -0.14px;
            margin: 24px 0px 0px 0px;
            text-transform: uppercase;
        }
        &__text {
            color: $dark-charcoal;
            /* Body/B1 */
            font-family: "Mona Sans";
            font-size: var(--Font-Size-md, 18px);
            font-style: normal;
            font-weight: 700;
            line-height: var(--Font-Line-height-lg, 24px);
            /* 133.333% */
            letter-spacing: -0.18px;
            margin: 0px;
        }
}

.how-it-works{
    margin-top: 40px;
    color: $dark-charcoal;
    font-family: "Mona Sans";
    font-size: 14px !important;
    font-style: normal;
    line-height: 190%; /* 26.6px */

    @media (max-width: 1320px) {
        margin-left: 5px;
    }
    
    &__title{
        font-weight: 700;
        margin-bottom: 0px;
    }

    &__list{
        font-weight: 500;
        padding-left: 15px;
        margin-top: 0px;
        
        @include rtl-styles{
            padding-left: 0px;
            padding-right: 15px;

        }
    }
}

.offer-brand-accordian{
    display: flex;
    flex-direction: column;
}

.amount-validity {
    &__container{
        display: flex;
        justify-content: space-between;
    }

    &__title {
        color: $grey-text;
        /* Body/B3 */
        font-family: var(--Font-Family-body, "Mona Sans");
        font-size: var(--Font-Size-xs, 14px);
        font-style: normal;
        font-weight: 500;
        line-height: var(--Font-Line-height-sm, 16px);
        /* 114.286% */
        letter-spacing: -0.14px;
        margin: 24px 0px 0px 0px;
        text-transform: uppercase;
    }
    &__text {
        color: $dark-charcoal;
        /* Body/B1 */
        font-family: "Mona Sans";
        font-size: var(--Font-Size-md, 18px);
        font-style: normal;
        font-weight: 700;
        line-height: var(--Font-Line-height-lg, 24px);
        /* 133.333% */
        letter-spacing: -0.18px;
        margin: 0px;
        margin-top: 5px;
    }
  
    .validity {
      text-align: left !important;
    }
  
    .applicable-amount {
      text-align: right;
    }
  
    @include rtl-styles {
      .validity {
        text-align: right !important;
      }
  
      .applicable-amount {
        text-align: left;
      }
    }
  }
  

  .offer-exhausted{
    display: flex;
    justify-content: center;
    margin: 30px 0px 30px 0px;
    
    &__content{
        background-color: #f2f5f8;
        padding: 16px 8px;
        width: 390px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;

        &--text{
            color: red;
            font-family: "Mona Sans";
        }
    }

    
  }