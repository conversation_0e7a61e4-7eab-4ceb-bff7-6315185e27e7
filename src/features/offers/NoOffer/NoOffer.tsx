import { useTranslation } from 'next-i18next';
import styles from './noOffer.module.scss';
import getConfig from 'next/config';
import { OFFER_TYPE } from '@constants/common';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
  } = getConfig();

const NoOffer = ({type}:{type?: OFFER_TYPE.PROMO_CODE | OFFER_TYPE.EXTRA_VALUE | null}) => {
    const { t } = useTranslation("common");

    return (
        <>
            <style jsx>
                {
                    `
                    .no-offer-content__image{
                        background: url('${imageBaseUrl}/images/no-offer-bg.png');
                        width: 205.758px;
                        height: 162.459px;
                        position: relative;
                    }
                    .no-offer-content__image2{
                        background: url('${imageBaseUrl}/images/no-offer-pic1.png');
                        position: absolute;
                        width: 182.834px;
                        height: 188.593px;
                        bottom: 0;
                        left: 24px;
                    }
                    .no-offer-content__circle-plus{
                        background: url('${imageBaseUrl}/images/no-offer-circle-plus.png');
                        position: absolute;
                        position: absolute;
                        bottom: -17px;
                        width: 40px;
                        height: 40px;
                        left: 65px;
                        z-index: 1;
                    }
                    .no-offer-content__circle{
                        background: url('${imageBaseUrl}/images/no-offer-plus.png');
                        position: absolute;
                        top: 10px;
                        left: 20px;
                        width: 21px;
                        height: 24px;
                    }
                    .no-offer-content__plus{
                        background: url('${imageBaseUrl}/images/no-offer-big-plus.png');
                        position: absolute;
                        top: -30px;
                        right: -20px;
                        width: 21px;
                        height: 21px;
                    }
                    .no-offer-content__percentage{
                        background: url('${imageBaseUrl}/images/no-offer-percentage.png');
                        position: absolute;
                        top: 40px;
                        left: -15px;
                        width: 24px;
                        height: 24px;
                    }
                    `    
                }
            </style>
            <div
        className={`${styles["no-offer-container"]} ${
          type && styles["no-offer-content--bg"]
        }`}
      >
        <div className={styles["no-offer-content"]}>
          <div
            className={`${styles["no-offer-content__image"]} no-offer-content__image`}
          >
            {type == OFFER_TYPE.EXTRA_VALUE ? (
              <img src={`${imageBaseUrl}/icons/no-offer.svg`} alt="no-offer" />
            ) : type == OFFER_TYPE.PROMO_CODE ? (
              <img
                src={`${imageBaseUrl}/icons/promo-un-available.svg`}
                alt="no-promo"
              />
            ) : (
              <>
                <div className="no-offer-content__image2"></div>
                <div className="no-offer-content__circle-plus"></div>
                <div className="no-offer-content__circle"></div>
                <div className="no-offer-content__plus"></div>
                <div className="no-offer-content__percentage"></div>
              </>
            )}
          </div>
          <div className={styles["no-offer-content__title"]}>
            {t("excitingNewOffers")}
          </div>
          <div className={styles["no-offer-content__subtitle"]}>
            {t("getHappyCardNow")}
          </div>
        </div>
      </div>
        </>

    )
}

export default NoOffer;