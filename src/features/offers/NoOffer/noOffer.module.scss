@import "@styles/variables";
@import "@styles/mixins";

.no-offer-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.no-offer-content {
    margin-top: 64px;
    color: $dark-charcoal;
    display: flex;
    flex-direction: column;
    align-items: center;

    &__image{
        margin-bottom: 32px;
    }


    &__subtitle {

        text-align: center;
        /* Body/B2 - Semi bold */
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        width: 491px;
        margin-top: 16px;

        /* 150% */
    }

    &__title {

        text-align: center;
        /* Heading/H1 */
        font-family: $bricolage-font-family; font-optical-sizing: none;
        font-size: 32px;
        font-style: normal;
        font-weight: 800;
        line-height: 40px;
        letter-spacing: -0.16px;
        margin-top: 32px;

        @include rtl-styles{
            font-family: $arabic-font-family;
          }
    }


  &--bg {
    background: $grey-primary;
    border-radius: 12px;
    padding: 24px 14px;

    .no-offer-content {
      margin-top: 0;
    }

    .no-offer-content__subtitle {
      width: auto;
      font-size: 10px;
      line-height: 16px;
      margin-top: 8px;
      letter-spacing: -0.1px;

      @include rtl-styles{
        line-height: 30px !important;
      }
    }

    .no-offer-content__title {
      font-size: 18px;
      line-height: 16px;
      margin-top: 24px;
      letter-spacing: -0.09px;

      @include rtl-styles{
        line-height: 30px !important;
      }
    }

    .no-offer-content__image {
      margin: 0;
      background: none !important;
      height: auto !important;
      width: auto !important;
      img {
        width: 162px;
      }
    }
  }
}