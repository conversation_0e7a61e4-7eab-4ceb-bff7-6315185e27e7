import Image from 'next/image';
import styles from './OffersPageLeftSection.module.scss'
import getConfig from 'next/config';
import OffersCard from '../OffersCard/OffersCard';
import { useEffect, useState } from 'react';
import OfferInfo from '../OfferInfo/OfferInfo';
import NoOffer from '../NoOffer/NoOffer';
import useAppRouter from '@features/common/router.context';
import { getPromoCodes } from '../offersAPI';
import Loader from '@features/common/loader/Loader';
import { useTranslation } from 'next-i18next';
import { OFFER_TYPE } from '@constants/common';

// #. Taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const OfferPageLeftSection = ({ code, brandImageData, name, brandOffers, brandPromos, offerSlug }: any) => {

  const { t } = useTranslation("common");
  const {
    state: { locale }
  } = useAppRouter();

  const [isOfferInfoPage, setIsOfferInfoPage] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<any>({});
  const [selectedPromo, setSelectorPromo] = useState<any>({});
  const [isOffer, setIsOffer] = useState<any>();
  const [loading, setLoading] = useState(false);



  useEffect(() => {
    prePopulateData();
  }, []);

  /**
   * Method to prepopulate offer data if slug is present
   */
  const prePopulateData = async () => {
    try {
      if (offerSlug) {
        const offerFound = brandOffers.filter((offer: any) => {
          return offer?.node?.brand?.slug == offerSlug
        });
        if (offerFound && offerFound.length) {
          setIsOffer(true);
          setIsOfferInfoPage(true);
          setSelectedOffer(offerFound[0]);
        } else {
          const offerFound = brandPromos.filter((offer: any) => {
            return offer?.node?.brand?.slug == offerSlug
          });
          if (offerFound && offerFound.length) {
            const promoData = await getPromoCodes(locale, offerFound[0]?.node?.brand?.code, code);
            setIsOffer(false);
            setIsOfferInfoPage(true);
            setSelectedOffer(offerFound[0]);
            setSelectorPromo(promoData?.data?.offersAndPromoCodes?.edges);
          } else {
            const newUrl = `${window.location.pathname.split('/offers')[0]}/offers`;
            window.history.replaceState({}, "", newUrl);
          }
        }
      }
    } catch (error) {
      console.log('Error occured in prePopulateData ', error);
    }

  }

  /**
   * Method to handle offer tile click
   * @param offerInfo 
   */
  const handleOfferClick = (offerInfo: any) => {
    try {
      setIsOffer(true);
      setSelectedOffer(offerInfo);
      setIsOfferInfoPage(true);
      const newUrl = `${window.location.pathname}${offerInfo?.node?.brand?.slug}/`;
      window.history.replaceState({}, "", newUrl);
      window.scrollTo({ top: 0, behavior: "smooth" });
      document.body.scrollTop = 0;
    } catch (error) {
      console.log('Error occured in handleOfferClick ', error);
    }
  }

  /**
   * Method to handle promo tile click
   * @param offerInfo 
   */
  const handlePromoClick = async (offerInfo: any) => {
    try {
      setLoading(true);
      const data = await getPromoCodes(locale, offerInfo?.node?.brand?.code, code);
      setLoading(false);
      setSelectorPromo(data?.data?.offersAndPromoCodes?.edges);
      setIsOffer(false);
      setSelectedOffer(offerInfo);
      setIsOfferInfoPage(true);
      const newUrl = `${window.location.pathname}${offerInfo?.node?.brand?.slug}/`;
      window.history.replaceState({}, "", newUrl);
      window.scrollTo({ top: 0, behavior: "smooth" });
      document.body.scrollTop = 0;
    } catch (error) {
      console.log('Error occured in handlePromoClick ', error);
    }
  }

  /**
   * Method to handle back button click
   */
  const handleBack = () => {
    try {
      const newUrl = `${window.location.pathname.split('/offers/')[0]}`;
      window.history.replaceState({}, "", `${newUrl}/offers/`);
      setIsOfferInfoPage(false);
    } catch (error) {
      console.log('error occured in handleBack ', error);
    }

  }

  /**
   * This method returns the offer promo tile html
   * @returns 
   */
  const brandOfferPromoTiles = () => {
    return (
      <>
        <div className={styles['offer-heading']}>
          <div className={styles['offer-title']}>
            <span>
              {t('buyunlockoffers', { name: name })}
            </span>
          </div>
        </div>
        <div className={styles['value-promo-offers']}>
          <div className={styles['value-promo-offers__title-container']}>
            <span className={styles['value-promo-offers__title-container--value-offer-text']}>
              {t('extravalueoffers')}
            </span>
            <span className={styles['value-promo-offers__title-container--promo-offer-text']}>
              {t('promocodeoffers')}
            </span>
          </div>
          <div className={styles['value-promo-offers__tiles-container']}>
            <div className={styles['value-promo-offers__tiles-container--value-offers']}>
              {brandOffers?.length ? (
                brandOffers.map((offer: any) => (
                  <OffersCard
                    key={offer.id}
                    item={offer}
                    onOfferClicked={handleOfferClick}
                  />
                ))
              ) : brandPromos?.length ? (
                <NoOffer type={OFFER_TYPE.EXTRA_VALUE} />
              ) : null}
            </div>

            <div
              className={
                styles["value-promo-offers__tiles-container--promo-offers"]
              }
            >
              {brandPromos?.length ? (
                brandPromos.map((promo: any) => (
                  <OffersCard
                    key={promo.id}
                    item={promo}
                    onOfferClicked={handlePromoClick}
                  />
                ))
              ) : brandOffers?.length ? (
                <NoOffer type={OFFER_TYPE.PROMO_CODE} />
              ) : null}
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
    <>
      <div className={styles['offer-page-left']}>
        {isOfferInfoPage && <OfferInfo onBackClick={handleBack} isOpen={true} offerData={selectedOffer?.node} brandImage={brandImageData} cardName={name} isOffer={isOffer} promoData={selectedPromo} />}

        {
          (!brandOffers || !brandOffers.length) && (!brandPromos || !brandPromos.length) && <NoOffer />
        }

        {
          !isOfferInfoPage && ((brandOffers && brandOffers.length > 0) || (brandPromos && brandPromos.length > 0)) && brandOfferPromoTiles()
        }

        {loading && <Loader />}
      </div>
    </>
  )
}

export default OfferPageLeftSection;