@import "@styles/variables";
@import "@styles/mixins";

.offer-section-container {}

.offer-page-left{
    display: flex;
    flex-direction: column;
    flex: 1;
    width: calc(100% - 496px);

    @media (max-width: ($md + 40)) {
        width: 100%;  
    }

    // @media (min-width: 1321px) and (max-width: 1440px) {
    //     width: calc(100% - 560px);
    // }
}

.value-promo-offers {
    &__title-container {
        margin-top: 32px;
        margin-bottom: 24px;
        gap: 32px;

        @media (max-width: ($md + 40)) {
            gap: 45px;  
          }

        &--value-offer-text, &--promo-offer-text {
            color: $dark-charcoal;
            font-family: "Mona Sans";
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: 24px;
            /* 100% */
            letter-spacing: -0.24px;
            flex: 1;
        }
    }

    &__tiles-container{
        display: flex;
        gap: 48px;

        // @media (min-width: 1321px) and (max-width: 1440px) {
        //     // gap: 30px;
        // }

        // @media (max-width: ($lg + 40)) {
        //     gap: 30px;
        // }

        &--value-offers, &--promo-offers{
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        

    }
}

.offer-heading {
    width: 100%;
    position: relative;
}

.offer-image-icon {
    width: 150px;
    height: 96px;
    position: relative;

    span {
        border-radius: 8px;
    }

    .plus-icon,
    .discount-icon {
        position: absolute;
        border-radius: 4px;
        background: $white;
        width: 30px;
        height: 30px;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        @media (max-width: ($lg + 40)) {
            width: 20px;
            height: 20px;
        }
        
    }

    .plus-icon{
        left: -17px;
        top: 40px;

        @media (max-width: ($lg + 40)) {
            left: -17px;
            top: 40px;
        }
    }

    .discount-icon{
        left: 140px;
        top: 80px;

        @media (max-width: ($lg + 40)) {
            left: 95px;
            top: 54px;
        }
    }
}

.offer-image{
    
}

.offer-title {
    width: 100%;
    height: 29px;
    color: $error-red;
    margin-top: 32px;
    margin-bottom: 64px;
    /* 150% */

    span{
        font-family: $bricolage-font-family;
        font-optical-sizing: none;
        font-size: 32px !important;
        font-style: normal;
        font-weight: 800;
        width: 100%;
        display: inline-block;
        text-align: center;
    }
}