import BrandValue from "@features/brand/brandValue/BrandValue";
import styles from './OfferBrandDenominationNBuy.module.scss';

const OfferBrandDenominationNBuy = ({ brand, brandInstance }: any) => {
    return (
        <div className={styles['denom-buy-container']}>
            <BrandValue
                slug={brand?.slug}
                brandCode={brand?.code}
                brandName={brand?.name}
                variableDenomination={brandInstance?.variableDenomination}
                minDenominationAmount={brandInstance?.minDenominationAmount}
                maxDenominationAmount={brandInstance?.maxDenominationAmount}
                currencyCode={brandInstance?.currency?.code}
                currencyDecimalNotation={brandInstance?.currencyDecimalNotation}
                isUserAuthorized={true}
                renderPreviewPage={brandInstance?.renderPreviewPage}
                fromOfferPage={true}
            />
        </div>
    )
}

export default OfferBrandDenominationNBuy;