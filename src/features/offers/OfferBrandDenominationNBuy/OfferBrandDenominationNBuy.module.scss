@import "@styles/variables";
@import "@styles/mixins";

.denom-buy-container{
    margin-top: 32px;
}

.brand-value {
    position: relative;

    /* stylelint-disable */
    .MuiInputLabel-root,
    .MuiInputLabel-root.Mui-focused {
      color: $dark-purple !important;
      font-weight: 500 !important;

      @include font-size-important(16);

      @include rtl-styles {
        display: contents;
        font-size: 10px;
      }
    }

    .MuiInput-root.MuiInputBase-root {
      &::before {
        border-bottom: 0 none !important;
      }

      &::after {
        border-bottom: 0 none !important;
      }

      input {
        @include font-size(34);

        font-weight: 600;
        color: $dark-purple;

        &[disabled] {
          opacity: 1;
          color: $dark-purple !important;
          -webkit-text-fill-color: $dark-purple !important;
        }
      }
    }

    .MuiFormHelperText-root {
      @include font-size(10);

      font-family: $default-font-family;
      color: $error-text;
    }

    .MuiInputAdornment-root {
      p {
        @include font-size-important(34);
        font-weight: 600 !important;
        font-family: $default-font-family !important;
        color: $dark-purple;

        @include rtl-styles {
          font-weight: 600 !important;
          padding: 0px 0px 0px 8px;
        }
      }
    }

    /* stylelint-enable */
  }