
import { initialize<PERSON><PERSON><PERSON> } from "@graphql/apolloClient";
import {DEFAULT_BRAND_OFFERS, DEFAULT_BRAND_PROMOS, DEFAULT_STORE_BRAND_INSTANCE_QUERY, DEFAULT_STORE_BRAND_QUERY, PROMO_QUERY} from './offers.query';
import * as Sentry from "@sentry/nextjs";
import { useQuery } from "@apollo/client";
import { OFFERS_AND_PROMO_QUERY } from "@features/brand/brand.query";
import { OFFER_TYPE } from "@features/giftsOpen/constants/constants";

// Initialize appllo client
export const getDefaultStoreBrand = (
    store: string,
    locale: string
) => {
    try {
        const apolloClient = initializeApollo(locale);
        return apolloClient.query({
            query: DEFAULT_STORE_BRAND_QUERY,
            variables: {
                countryCode: store?.toUpperCase(),
            },
        });
    } catch (error) {
        Sentry.captureException(`Error fetching DEFAULT_STORE_BRAND_QUERY: ${error}`);
        console.error("Error fetching DEFAULT_STORE_BRAND_QUERY:", error);
    }
}

export const getDefaultBrandOffers = (
  store: string,
  locale: string
) => {
  try {
      const apolloClient = initializeApollo(locale);
      return apolloClient.query({
          query: DEFAULT_BRAND_OFFERS,
          variables: {
              countryCode: store?.toUpperCase(),
          },
      });
  } catch (error) {
      Sentry.captureException(`Error fetching DEFAULT_BRAND_OFFERS: ${error}`);
      console.error("Error fetching DEFAULT_STORE_BRAND_QUERY:", error);
  }
}

export const getDefaultBrandPromos = (
  store: string,
  locale: string
) => {
  try {
      const apolloClient = initializeApollo(locale);
      return apolloClient.query({
          query: DEFAULT_BRAND_PROMOS,
          variables: {
              countryCode: store?.toUpperCase(),
          },
      });
  } catch (error) {
      Sentry.captureException(`Error fetching DEFAULT_BRAND_PROMOS: ${error}`);
      console.error("Error fetching DEFAULT_BRAND_PROMOS:", error);
  }
}

export const getPromoCodes = (locale: string, brandCode: string,
  genericBrandCode: string) => {
  try {
    const apolloClient = initializeApollo(locale);
    return apolloClient.query({
      query: PROMO_QUERY,
      variables: {
        brandCode,
        genericBrandCode,
        offerType: OFFER_TYPE.PROMO_CODE
      },
    });
  } catch (error) {
    Sentry.captureException(`Error fetching OFFERS_AND_PROMO_QUERY: ${error}`);
    console.error("Error fetching OFFERS_AND_PROMO_QUERY:", error);
  }
}

export const fetchDefaultStoreBrandInstance = (locale: string, store: string) => {
    const {
      loading: brandInstanceLoading,
      error: brandInstanceError,
      data: brandInstanceData
    } = useQuery<any>(DEFAULT_STORE_BRAND_INSTANCE_QUERY, {
      variables: {
        countryCode: store?.toUpperCase(),
      },
      context: {
        headers: {
        "accept-language": locale,
        }
      },
      skip: !Boolean(store)
    });
    // #. Don not return data, must be take from the state method
    return {
        brandInstanceLoading,
        brandInstanceError,
        brandInstanceData
      };
}
