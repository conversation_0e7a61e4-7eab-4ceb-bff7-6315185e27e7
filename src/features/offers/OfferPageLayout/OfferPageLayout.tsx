import { useEffect, useState } from "react";
import { useAppSelector } from "@redux/hooks";
import { getTokenInfo } from "@features/common/commonSlice";
import useAppRouter from "@features/common/router.context";
import { Brand, RecommendedGiftCards } from "@interfaces/common.inteface";
import { HTTP_STATUS_CODE } from "@constants/statusCodes";
import useBrandAPI from "@features/brand/brandAPI";
import useBrand, { BrandContextAction } from "@features/brand/brand.context";
import { CARD_PURCHASE_TYPE, PURCHASE_ORIGIN } from "@features/brand/constants/brand.constant";
import OfferPageLeftSection from "../OffersPageLeftSection/OffersPageLeftSection";
import OffersPageRightSection from "../OffersPageRightSection/OffersPageRightSection";
import styles from './OffersPageLayout.module.scss';
import HomeDownloadApp from "@features/home/<USER>/HomeDownloadApp";
import { fetchDefaultStoreBrandInstance } from "../offersAPI";

interface BrandPageLayoutInterface {
  brandSlug: string;
  brandData: Brand;
  referenceId?: string; // Personalised data saved reference id from querystring "pedit"
  recommendedGiftCardsData: RecommendedGiftCards[] | Brand[];
  displayName?: string;
  blacklistedCountries?: [],
  brandOffers? :[]
  brandPromos?: []
  offerSlug?: string
}

/**
 * @method BrandPageLayout
 * @description Brand quick info
 * @returns
 */
const OfferPageLayout = ({
  brandSlug,
  brandData,
  displayName,
  brandOffers,
  brandPromos,
  offerSlug
}: BrandPageLayoutInterface): JSX.Element => {
  // #. Loader state
  const [loadingData, setLoadingData] = useState(true);

  // #. Get brand API
  const {
    getGuestUserData,
  } = useBrandAPI();

  const {
    state: { card },
    dispatch,
  } = useBrand();

  const tokenInfo = useAppSelector(getTokenInfo);

  const {
    state: { activeRegion, region, locale },
    router,
  } = useAppRouter();

  const { guestUserInfoError }: any =
  getGuestUserData(
    tokenInfo,
    activeRegion?.node?.code,
    tokenInfo?.isGuestUser,
  );
  // #. Fetch brand data on client side
  const { brandInstanceData , brandInstanceLoading } = fetchDefaultStoreBrandInstance(locale, region);
  
  // #. State to manage client fetched datae
  const [brandDataFromClient, setBrandDataFromClient] : any = useState(brandInstanceData?.defaultStoreBrand);

  const brand = brandData;

  /**
   * @method dispatchBrandContext
   * @param value
   */
  const dispatchBrandContext = (value: string | number | Object) => {
    dispatch({ type: BrandContextAction.CARD, payload: value });
  };

  useEffect(() => {
    // ## Setting the purchase type and purchase origin
    
    dispatchBrandContext({ 
      purchaseType: CARD_PURCHASE_TYPE.SELF,
      purchaseOrigin: PURCHASE_ORIGIN.OFFER,
    });
  }, []);

  useEffect(() => {
    setBrandDataFromClient(brandInstanceData?.defaultStoreBrand)
  },[brandInstanceData, brandInstanceLoading])

  useEffect(() => {
    if (guestUserInfoError?.graphQLErrors) {
      const errorStatus: any = guestUserInfoError?.graphQLErrors[0]?.status;
      if ([HTTP_STATUS_CODE.SERVICE_UNAVAILABLE,HTTP_STATUS_CODE.ACCESS_DENIED].includes(errorStatus)){
        router.push({
          pathname: `/`,
        });
      }
    }
  }, [guestUserInfoError]);

  return (
    <>
      <div className={`container ${styles['main-section']}`}>
        <div className={styles['offer-brand-left-right-section']}>
          <OfferPageLeftSection {...brand} brandOffers={brandOffers} brandPromos={brandPromos} offerSlug={offerSlug} />
          <OffersPageRightSection brand={brand} brandInstance={brandDataFromClient} loading={loadingData} brandInstanceLoading={brandInstanceLoading} />
        </div>
      </div>
    </>
  );
};

export default OfferPageLayout;
