import React from "react";
import styles from "./OffersCard.module.scss"
import getConfig from "next/config";
import useAppRouter from '@features/common/router.context';
import { useTranslation } from 'next-i18next';
import Link from "next/link";

const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

interface HappyYouOffersInterface {
  item: any;
  onOfferClicked: (value: any) => void;
}

export default function OffersCard(props: HappyYouOffersInterface) {

  const { item, onOfferClicked } = props;

  // #. Get Locale
  const {
    state: { locale },
  } = useAppRouter();

  const { t } = useTranslation("common");

  const {
    node: {
      offerHeader = "",
      promoLabel = "",
      offerText = "",
      brand: {
        logoImageData: brandLogo = "",
        name: brandName = "",
        code: brandCode = "",
        slug = ""
      } = {},
      hasOfferLabelDisabled = false,
      isDisabled: offerIsDisabled = false,
    } = {},
  } = item || {};

  const plusIcon = `${imageBaseUrl}/icons/plus-icon.svg`;
  const percentageIcon = `${imageBaseUrl}/icons/percentage-icon.svg`;


  return (
    <>
      <div
        className={`${styles["offers-card"]} ${offerIsDisabled ? styles["offers-card__disabled"] : ""
          }`}
        onClick={(e: any) => {
          e.preventDefault();
          onOfferClicked(item);
        }}
      >
        <div className={`${styles["offers-card__offer-nudge"]} ${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
          }`}>
          <p>{t('offer')}</p>
        </div>
        {offerIsDisabled && (
          <div className={styles["offers-card__not-available"]}>
            <span>{t("offerNotAvailable")}</span>
          </div>
        )}

        <div className={styles["offers-card__logo"]}>
          <img
            src={brandLogo}
            className={`${styles["offers-card__image"]} ${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
              }`}
            width={70}
            height={70}
          />
        </div>
        <div className={styles["offers-card__devider"]}></div>
        <div className={styles["offers-card__details"]}>
          <Link legacyBehavior href={`${slug}`}>
            <a onClick={(e: any) => { e.preventDefault(); }}>
              <h5
                className={`${styles["offers-card__details--title"]} ${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
                  }`}
              >
                {brandName}
              </h5>
            </a>
          </Link>

          {offerText && <div
            className={`${styles["offers-card__details--offer"]} ${hasOfferLabelDisabled || offerIsDisabled ? styles["offers-card__img-disabled"] : ""
              }`}
          >
            <img
              src={plusIcon}
              className={`${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
                }`}
            />
            <h5>{offerText}</h5>
          </div>}
          {promoLabel && <div
            className={`${styles["offers-card__details--offer"]} ${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
              }`}
          >
            <img
              src={percentageIcon}
              className={`${offerIsDisabled ? styles["offers-card__img-disabled"] : ""
                }`}
            />
           
                <h5>{promoLabel}</h5>
            

          </div>}
        </div>
        <div className={`${styles["offers-card__details--shape"]}`}></div>
      </div>
    </>
  );
}
