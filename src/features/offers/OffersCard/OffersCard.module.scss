@import '@styles/variables';
@import "@styles/mixins";


.offers-card {
    background: $semi-dark-grey;
    padding: 12.5px 9.5px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    width: 336px;
    height: 100px;

    // @media (min-width: 1321px) and (max-width: 1440px) {
    //     width: 295px;
    // }

    &__disabled {
        // background: rgba(255, 255, 255, 0.8);
        cursor: default !important;
    }

    &__not-available {
        position: absolute;
        top: 0px;
        left: 80px;
        background-color: #ffefe5;
        font-family: Poppins;
        font-size: 8px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-align: center;
        color: #ed1e25;
        padding: 2px 6px;
        border-radius: 0 0 4px 4px;

        @include rtl-styles {
            font-family: $arabic-font-family;
            left: 65px;
        }
    }

    &__img-disabled {
        filter: grayscale(100%);
        opacity: 0.5;
    }

    @media (max-width: ($lg + 40)) {
        width: calc(100% - 25px)
    }

    // @media (min-width: 1321px) and (max-width: 1440px) {
    //     width: 277px
    // }

    // @media (max-width: ($lg + 40)) {
    //     width: 268px
    // }

    // @media (max-width: 1200px) {
    //    width: 213px;
    // }

    @media (max-width: ($md + 40)) {
        width: 100%;
    }

    &--single {
        width: 257px;
    }

    &__logo {
        min-width: 40px;
        height: 70px;
        border-radius: 6px;
        border: solid 0.4px $light-grey;
        overflow: hidden;
        margin-left: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        @include rtl-styles {
            margin-right: 12px;
        }
    }

    &__devider {
        width: 2px;
        height: 80px;
        flex-grow: 0;
        border-right: 2px dotted rgb(0 -1 0 / 15%);
        background-color: #fff;
        margin-left: 8px;
    }

    &__details {
        margin-left: 8px;
        width: 68%;

        @include rtl-styles {
            margin:0;
        }

        h5 {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 86%;
            font-size: 12px;
            line-height: 1.3;
        }

        &--title {
            color: $dark-purple;
            font-weight: 600;
            margin-bottom: 6px !important;
            font-size: 16px !important;
            font-family: "Mona Sans";
        }

        &--title-disabled {
            filter: grayscale(100%);
        }

        &--offer {
            display: flex;

            img {
                margin-right: 5px;

                @include rtl-styles {
                    margin:0 0 0 5px;
                }
            }

            >h5 {
                color: $oops-red;
                font-weight: 600;
                font-family: "Mona Sans";
                font-size: 14px;
                font-style: normal;

                @media (max-width: ($lg + 40)) {
                    max-width: 90px;
                }
            }
        }

        &--offer:nth-of-type(2) {
            margin-top: 5px;
        }

        &--offer-disabled {
            filter: grayscale(100%);
            opacity: 0.5;
        }

        &--shape {
            height: 25px;
            width: 21px;
            border-radius: 50%;
            background-color: $white;
            position: absolute;
            right: -11px;

            @include rtl-styles {
                right: unset;
                left: -11px;
            }
        }
    }

    &__offer-nudge {
        position: absolute;
        left: 0px;
        border-radius: 0;
        width: 16px;
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f8450b;
        border-radius: 0 8px 8px 0;

        @include rtl-styles {
            right: 0;
            border-radius:  8px 0 0 8px;
        }

        >p {
            color: #fff;
            transform: rotate(180deg);
            writing-mode: vertical-lr;
            font-family: "Mona Sans";
            font-size: 11px;
            font-style: normal;
            font-weight: 700;
        }
    }

}

.no-cursor{
    cursor: default !important;
}