// Media.tsx
import { createMedia } from "@artsy/fresnel";

const AppMedia = createMedia({
    breakpoints: {
        md: 0,
		lg: 1065,
    },
    interactions: {
      hover: "(hover: hover)",
      notHover: "(hover: none)",
      landscape: "not all and (orientation: landscape)",
      portrait: "not all and (orientation: portrait)",
    },
  })
  
  export const { Media, MediaContextProvider, createMediaStyle } = AppMedia;