import { useTranslation } from "next-i18next";
import styles from "./TabSelection.module.scss";
import Link from "next/link";
import { useEffect, useState } from "react";
import Marquee from "react-fast-marquee";
import useAppRouter from "@features/common/router.context";
import MyAccount from "../../major/myAccount/MyAccount";
import { useAppSelector } from "@redux/hooks";
import { getTokenInfo } from "@features/common/commonSlice";
import { PAGEURLS } from "@constants/common";

/**
 * @method TabSelectionHeader
 * @description Handles tab button selection and page routing.
 * @returns {JSX.Element}
 */
const TabSelection = ({ tabs, activeTab, dropdownProps}: any): JSX.Element => {
  const {
    state: { locale },
  } = useAppRouter();

  const { isUserSignedIn } = useAppSelector(getTokenInfo);

  const {dropdown, isGuestUser, setDropdown, signinPanelEvent } = dropdownProps;

  // #. Get current screen width
  const [screenWidth, setScreenWidth] = useState<any>();

  // #. Taranslation
  const { t } = useTranslation("common");

  useEffect(() => {
    setScreenWidth(window.innerWidth);
  }, [screenWidth]);

  /**
   * @method getCurveClasses
   * @description: This handles the logic for adding curved psudo element classes to the tabs left and right of the active tab. (for the white border between each tab intersections)
   * @returns {string}
   */
  const getCurveClasses = (
    index: number,
    activeIndex: number,
    tabsLength: number
  ) => {
    const classes = [];

    const AFTER_RANGE = Math.min(4, tabsLength - 1);
    const BEFORE_RANGE = Math.min(3, tabsLength - 1);

    const isWrapAround =
      (activeIndex === tabsLength - 1 && index === 0) ||
      (activeIndex === 0 && index === tabsLength - 1);

    const isFirstAfter =
      index === activeIndex + 1 && activeIndex !== tabsLength - 1; // only if active is NOT last

    if (isFirstAfter) {
      classes.push(styles.borderBothSides);
    } else if (
      (index > activeIndex && index <= activeIndex + AFTER_RANGE) ||
      (index < activeIndex && index >= activeIndex - BEFORE_RANGE) ||
      isWrapAround ||
      (activeIndex === tabsLength - 1 && index === activeIndex - 1) // left-only for last tab
    ) {
      classes.push(styles.borderLeftOnly);
    }

    return classes.join(" ");
  };

  return (
    <div className={styles.tabs}>
      {tabs.map((tab: any, index: number) => {
        // Determine the index of the active tab
        let activeIndex = tabs.findIndex((t: any) => t?.active);

        if (activeIndex === -1) {
          activeIndex = tabs.findIndex((t: any) => t.link?.includes(activeTab));
        }

        activeIndex = activeIndex === -1 ? 0 : activeIndex;

        const validActiveTab = tabs[activeIndex].link;
        const totalTabs = tabs.length;

        let zIndex;

        if (index === activeIndex) {
          zIndex = 99999;
        } else {
          zIndex = index + 1;
        }
        let hideCartIcon = tab.cartQuantity <= 0;
        let isCartTab = tab.link === PAGEURLS.CART;
        let isCartTabActive = tab.active;

        // #. Prevents navigation
        const disableClick = (
          event: React.MouseEvent<HTMLAnchorElement, MouseEvent>
        ) => {
          event.preventDefault();
        };

        return (
          <Link legacyBehavior key={tab.link} href={tab.link} shallow>
            <a
              className={`${styles.tab} ${
                validActiveTab === tab.link ? styles.active : ""
              } ${isCartTab ?styles['cart-tab'] : ""}`}
              style={{
                backgroundColor: tab.bgColor,
                ...(tab?.fontColor && { color: tab.fontColor }),
                opacity: validActiveTab === tab.link ? 1 : 0.8,
                zIndex: zIndex,
                backgroundImage: tab.bgImageUrl ? `url(${tab.bgImageUrl})` : "",
                backgroundPosition: "center",
              }}
              onClick={tab.disableCTA && disableClick}
            >
              <div
                className={`${styles.curve} ${getCurveClasses(
                  index,
                  activeIndex,
                  totalTabs
                )}`}
              />
              {tab?.content ? (
                tab?.content
              ) : (
                <h1>
                  {(screenWidth <= 1280 && tab.label?.length > 13) ||
                  (
                    // screenWidth <= 1380 &&
                    locale == "ar" &&
                    tab.label?.length >= 13) ? (
                    <Marquee
                      className={styles.marquee}
                      gradient={true}
                      gradientColor={tab.bgColor}
                      gradientWidth={10}
                      direction={locale === "ar" ? "right" : "left"}
                    >
                      {tab.label}
                      <span style={{ marginRight: "18px" }}></span>
                    </Marquee>
                  ) : (
                    <>{tab.label}</>
                  )}
                </h1>
              )}
            </a>
          </Link>
        );
      })}

      {dropdown && (
        <MyAccount
          mobile={false}
          isGuestUser={isGuestUser}
          setDropdown={setDropdown}
          redirectToSignInPage={signinPanelEvent}
        />
      )}
    </div>
  );
};

export default TabSelection;
