@import "@styles/variables";
@import "@styles/mixins";

.tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.tab {
  border-radius: calc(2 * 36px) calc(2 * 36px) 0 0 / 38px; /* Match mask radius */
  cursor: pointer;
  transition: all 0.3s ease;
  /* Keep the mask properties */
  border-inline: 36px solid #0000;
  /* Match mask radius */
  mask: radial-gradient(36px at 36px 0px, #0000 99%, #000 0%) -36px 100%/100% 36px repeat-x, conic-gradient(#000 0 0) padding-box;
  -webkit-mask: radial-gradient(36px at 36px 0px, #0000 99%, #000 0%) -36px 100%/100% 36px repeat-x, conic-gradient(#000 0 0) padding-box;

  opacity: 1 !important;

  /* Adjustments for size and alignment */
  margin: 0 -36px;
  height: 76px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 312px;

  &--cart {
    justify-content: flex-end;
    flex-direction: row-reverse;
  }

  > h1 {
    @include font-size(24);
    font-family: $bricolage-font-family; font-optical-sizing: none;
    text-transform: capitalize;
    text-align: center;
    width: 100%;
    font-weight: 800;

    span{
      font-family: $default-font-family !important;
      @include font-size(24);
      font-style: normal;
      font-weight: 800;
      line-height: 40px;
    }

    @include rtl-styles {
      font-family: $arabic-font-family !important;
    }
  }

  // @media (max-width: ($xxl + 40)) {
  //   width: 290px;
  // }

  @media (min-width: 1401px) and (max-width: 1600px) {
    width: 312px ;
  }

  @media (min-width: 1321px) and (max-width: 1400px) {
    width: 290px;
  }
  
  // @media (max-width: $xxl) {
  //   width: 280px;
  // }

  @media (max-width: ($lg + 40)) {
    width: 280px;
  }

  @media (max-width: ($lg - 10)) {
    width: 260px;
  }

  @media (max-width: 1160px) {
    width: 234px;
  }
}

.tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: -38px;
  right: -38px;
  bottom: 0;
  border-radius: calc(2 * 36px) calc(2 * 36px) 0 0 / 36px; /* Match tab radius */
  border: 3px solid white; /* Border color changed to white */
  pointer-events: none; /* Ensure it doesn’t block interactions */
  -webkit-mask: radial-gradient(36px at 36px 0, #0000 98%, #000 101%)
      calc(-1 * 36px) 100%/100% 36px repeat-x,
    conic-gradient(#000 0 0) padding-box;
  mask: radial-gradient(36px at 36px 0, #0000 98%, #000 101%) calc(-1 * 36px)
      100%/100% 36px repeat-x,
    conic-gradient(#000 0 0) padding-box;
}

.tab.active::before {
  border-bottom: none; /* Remove bottom border for active tab */
}

.curve {
  position: relative;
}

.curve.borderBothSides::after,
.curve.borderBothSides::before {
  content: "";
  position: absolute;
  width: 76px;
  height: 76px;
  border: 3px solid white;
  border-radius: 50%;
  background-color: transparent;
  top: -42px;
  left: -0.6px;
  clip-path: inset(0 50% 0 0);

  @include rtl-styles {
    left: -81px;
    clip-path: inset(0 0 0 50%);
  }
}

.curve.borderBothSides::before {
  left: 159px;
  clip-path: inset(0 0 0 50%);

  @media (min-width: 1321px) and (max-width: 1400px) {
    left: 137px;
  }

  @media (min-width: 1271px) and (max-width: 1320px) {
    left: 127px;
  }

  @media (min-width: 1161px) and (max-width: 1270px) {
    left: 107px;
  }

  @media (min-width: 1100px) and (max-width: 1160px) {
    left: 81px;
  }

  @include rtl-styles {
    left: -240px;
    clip-path: inset(0 50% 0 0);

    @media (min-width: 1352px) and (max-width: 1400px) {
      left: -218px;
    }

    @media (min-width: 1321px) and (max-width: 1351px) {
      left: -216px;
    }

    @media (min-width: 1161px) and (max-width: 1270px) {
      left: -189px;
    }

    @media (min-width: 1281px) and (max-width: 1320px) {
      left: -209px;
    }

    @media (min-width: 1271px) and (max-width: 1280px) {
      left: -196px;
    }
    @media (max-width: 1160px) {
      left: -162px;
    }
  }
}

.curve.borderLeftOnly::before {
  content: "";
  position: absolute;
  width: 76px;
  height: 76px;
  border: 3px solid white;
  border-radius: 50%;
  background-color: transparent;
  top: -42px;
  left: 159px;
  clip-path: inset(0 0 0 50%);
  
  // #. responsive for arabic
  @include rtl-styles {
    left: -241px;
    clip-path: inset(0 50% 0 0);

    @media (max-width: ($xxl + 40)) {
      left: -240px;
    }

    @media (max-width: ($xxl)) {
      left: -219px;
    }

    @media (min-width: 1271px) and (max-width: 1280px) {
      left: -196px !important;
    }

    @media (max-width: ($xl + 40)) {
      left: -217px;
    }

    @media (max-width: $xl) {
      left: -209px;
    }

    @media (max-width: ($lg - 10)) {
      left: -189px;
    }

    @media (max-width: (1160px)) {
      left: -163px;
    }
  }

  @media (max-width: $xxl) {
    left: 137px;
  }

  @media (max-width: $xl) {
    left: 127px;
  }

  @media (max-width: ($lg - 10)) {
    left: 107px;
  }

  @media (max-width: 1160px) {
    left: 81px;
  }
}

.marquee {
  z-index: -1;
  @include font-size(24);
}

.cart-tab{
    padding: 0 23px;
    width: auto;
}
