import { useTranslation } from 'next-i18next';
import React from 'react';
import styles from "../../major/MajorHeader.module.scss"
import getConfig from 'next/config';

const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();


/**
 * @method LanguageSwitch
 * @description Component to show language switch option for arabic available countries.
 */
export const LanguageSwitch = ({
  switchLanguage,
}: {
  switchLanguage: () => void;
}) => {
  // #. Get translated messages
  const { t } = useTranslation("common");
  const TranslateIcon = `${imageBaseUrl}/icons/revamp/language-switch.svg`;

  return (
    <>
      <h5>{t("changeLanguage")}</h5>
      <div
        className={styles["stores-list__lang-switch"]}
        onClick={() => switchLanguage()}
      >
        {t("langSwitch")}
        <img src={TranslateIcon} alt={""} />
      </div>
    </>
  );
};
