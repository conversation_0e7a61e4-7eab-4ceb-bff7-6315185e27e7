import React, { useEffect, useRef, useState, forwardRef } from "react";
import styles from "./TabSelectionHeader.module.scss";
import Link from "next/link";
import { supportWebP } from "@utils/detectWebp";
import { HEADER_TYPE, PAGEURLS, WIDGET_TYPE } from "@constants/common";
import { headerNodeInterface, StoresItem } from "@interfaces/common.inteface";
import StoreSelector from "../major/StoreSelector";
import useAppRouter, { DEFAULT_LOCALE, ROUTER_CONTEXT_ACTION } from "@features/common/router.context";
import { useAppDispatch, useAppSelector } from "@redux/hooks";
import { brandCountryName } from "@features/brand/brandCardSchedule/brandScheduleSlice";
import UserAccountInfo from "../major/UserAccountInfo";
import CartInfo from "../major/CartInfo";
import getConfig from "next/config";
import TabSelection from "./tabSelection/TabSelection";
import { useTranslation } from "next-i18next";
import { WorkSelection } from "./work/WorkSelection";
import MyAccount from "../major/myAccount/MyAccount";
import { getAtWorkEnabled, getTokenInfo } from "@features/common/commonSlice";
import useCart from "@features/cart/cartSlice";
import Footer from "@features/common/footer/Footer";
import CustomerReviews from "@features/home/<USER>/CustomerReviews";
import SolutionHUbBanner from "@features/home/<USER>/SolutionHubBanner";
import HomeDownloadApp from "@features/home/<USER>/HomeDownloadApp";

// taking public image config url
const {
  publicRuntimeConfig: { authRedirectUrl, basePath, groupGiftUrl ,imageBaseUrl, solutionsHubURL}
} = getConfig();

interface TabSelectionHeaderInterfce {
  children: any;
  headerData: headerNodeInterface[];
  storesDataItem: StoresItem[];
  disableStoreSelection?: boolean;
  isUserSignedIn?: boolean;
  isGuestUser?: boolean;
  listOpened?: boolean;
  footerData?: any;
  widgetsLineup?: any;
  page?: string;
  ref?: any
  solutionHubEnabled?: boolean;
}

interface TabInterface {
  label?: string;
  bgColor: string;
  link: string;
  fontColor?: string;
  bgImageUrl?: string;
  active?: boolean;
  content?: any;
  disableCTA?: boolean;
  cartQuantity?: number;
}

/**
 * @method TabSelectionHeader
 * @description Header component with tab selection
 * @returns {JSX.Element}
 */
const TabSelectionHeader = forwardRef<HTMLDivElement, TabSelectionHeaderInterfce>(({
  children,
  headerData,
  storesDataItem,
  disableStoreSelection,
  isUserSignedIn = false,
  isGuestUser = false,
  listOpened = false,
  footerData,
  widgetsLineup,
  page,
  solutionHubEnabled = false,
}, ref): JSX.Element => {
  // use app dispatch
  const reduxDispatch = useAppDispatch();
  const { t } = useTranslation("common");
  const [webpSupported, setWebpSupported] = useState<boolean>(false);
  const [dropdown, setDropdown] = useState<boolean>(false);
  const [isOverlapping, setIsOverlapping] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);
  const footerRef = useRef<HTMLDivElement>(null);

  const [isHidden, setIsHidden] = useState(false);
  

  const {
    state: { locale, activeRegion },
    dispatch,
    redirect,
    router,
  } = useAppRouter();

  // #. Get the current region
  const currentRegion = activeRegion;
  const currentRegionCode =
    currentRegion?.node?.country?.code?.toLowerCase() || "ae";

  const data = headerData?.filter(
    ({ node }) => node?.headerType?.code === HEADER_TYPE.MAJOR
  );

  // Major Heading logo
  const logo = data[0]?.node?.logo;
  const logoWebp = data[0]?.node?.logoWebp;

  const backgroundImage = `${imageBaseUrl}/images/purple-bg.png`;
  const groupGiftSiteURL = groupGiftUrl + `${locale}-${currentRegionCode}`;

  const activeTab = router.asPath;
  const isCartTabActive =
    activeTab?.includes(PAGEURLS.CART) || activeTab?.includes("account") || activeTab?.includes("payment");
  const isWorkTabActive = activeTab?.includes("work");

  const atWorkEnabled = useAppSelector(getAtWorkEnabled);
  const { getBasicCartData } = useCart();
  const { totalQuantity } = useAppSelector(getBasicCartData);
  const tokenInfo = useAppSelector(getTokenInfo);

  
  /**
   * @method signinPanelEvent
   * @description redirects to sign in page
   */
  const signinPanelEvent = () => {
    router.push(
      {
        pathname: `${authRedirectUrl}/${locale}/login/`,
        query: {
          rdir: `${window.location.origin.toString()}/${basePath}/${
            router?.asPath
          }`,
          store: router?.locale,
        },
      },
      undefined,
      {
        locale,
      }
    );
  };

  // #. Tab data for each page
  const tabs: TabInterface[] = [
    {
      label: t("gifting"),
      bgColor: "var(--default-bg-color)",
      link: PAGEURLS.HOME,
    },
    {
      label: t("groupGifting"),
      bgColor: "#D2C9FF",
      link: groupGiftSiteURL,
    },
    {
      label: t("happyYouOffers"),
      bgColor: "#FF9B9B",
      link: PAGEURLS.OFFERS,
    },
    // {
    //   label: t("gaming"),
    //   bgColor: "#b801d4",
    //   link: `${PAGEURLS.CATERGORIES + PAGEURLS.GAMING_GIFT_CARD + PAGEURLS.ALL_BRANDS}/`,
    //   fontColor: "white",
    //   bgImageUrl: backgroundImage,
    // },
    {
      bgColor: isCartTabActive ? "#f5f7f5" : "white",
      link: PAGEURLS.CART,
      active: isCartTabActive,
      disableCTA: true,
      cartQuantity: totalQuantity,
      content: (
        <>
          <div className={`${styles["header__cart-selection"]}`}>
            <UserAccountInfo
              isUserSignedIn={isUserSignedIn}
              isGuestUser={isGuestUser}
              setDropdown={setDropdown}
            />
            {(isUserSignedIn || tokenInfo?.isGuestUser) && (
              <CartInfo
                isUserSignedIn={isUserSignedIn}
                isCartTabActive={isCartTabActive}
              />
            )}
          </div>
        </>
      ),
    },
  ];

  const activeTabData = tabs.find((tab) => tab.link === activeTab);

  // #. Props for user action dropdown 
  const dropdownProps = {
    dropdown,
    isGuestUser,
    setDropdown,
    signinPanelEvent,
  };
  

  // region dropdown selection
  const selectRegion = (code: string, name: string) => {
    const currentCode = code.toLowerCase();

    pushURL(currentCode, DEFAULT_LOCALE);
    reduxDispatch(brandCountryName({ name }));
  };

  /**
   * url redirection with param
   * @param region : Selected Region
   * @param path : redirection path ( current path)
   * @param locale : locale
   */
  const pushURL = (region: string, locale: string) => {
    redirect(region, locale);
  };

  useEffect(() => {
    const bgColor =
      isWorkTabActive ? "#99C6FF" :
      isCartTabActive ? "#F5F5F5" :
      activeTabData?.bgColor || "var(--default-bg-color)";
  
    document.documentElement.style.setProperty("--after-bg-color", bgColor);
  }, [activeTab]);
  

  useEffect(() => {
    supportWebP().then((result: any) => setWebpSupported(result));
  }, []);

  // #. Dispatch stores data
  useEffect(() => {
    dispatch({
      type: ROUTER_CONTEXT_ACTION.ALL_REGION,
      payload: storesDataItem,
    });
  }, [storesDataItem]);

// #. Checks if header is overlapping with footer 
const checkOverlap = () => {
  const topEl = headerRef.current;
  const footerEl = footerRef.current;

  if (!topEl || !footerEl) return;

  const topRect = topEl.getBoundingClientRect();
  const footerRect = footerEl.getBoundingClientRect();

  const verticalBuffer = 20;
  const distance = footerRect.top - topRect.bottom;

  setIsOverlapping(distance <= verticalBuffer);
};

useEffect(() => {
  window.addEventListener("scroll", checkOverlap);
  window.addEventListener("resize", checkOverlap);

  checkOverlap();

  return () => {
    window.removeEventListener("scroll", checkOverlap);
    window.removeEventListener("resize", checkOverlap);
  };
}, []);


const contentRef = useRef<HTMLDivElement | null>(null);


useEffect(() => {
  const observer = new IntersectionObserver(
    ([entry]) => {
      setIsHidden(entry.isIntersecting);
    },
    { threshold: 0.7 }
  );

  if (contentRef.current) {
    observer.observe(contentRef.current);
  }

  return () => {
    if (contentRef.current) {
      observer.unobserve(contentRef.current);
    }
  };
}, []);

useEffect(() => {
  const handleScroll = () => {
    if (!contentRef.current) return;
    
    const solutionHubRect = contentRef.current.getBoundingClientRect();
    const scrollPosition = window.scrollY;
    const solutionHubTop = solutionHubRect.top + scrollPosition;

    if(scrollPosition >= solutionHubTop){
      setIsHidden(true);
    }
  };

  window.addEventListener("scroll", handleScroll);
  return () => window.removeEventListener("scroll", handleScroll);
}, []);

  return (
    <>
      <header 
        ref={headerRef} 
        className={`${styles["tab-selection"]} ${isHidden ? styles['tab-selection--hide-pseudo'] : ''}`}
      >
        <div className={`${styles.wrapper} ${styles.header}`}>
          <div className={`${styles["header__left"]}`}>
            <Link legacyBehavior href={`/`}>
              <a className={styles.logo}>
                <img
                  src={webpSupported ? logoWebp : logo}
                  alt="YouGotaGift"
                  className={styles["logo__image"]}
                />
              </a>
            </Link>
            <StoreSelector
              currentRegion={currentRegion}
              storesDataItem={storesDataItem}
              onStoreChanged={selectRegion}
              disableStoreSelection={disableStoreSelection}
            />
          </div>
          <TabSelection tabs={tabs} activeTab={activeTab} dropdownProps={dropdownProps}/>
          {atWorkEnabled && (
            <div className={`${styles["header__right"]}`}>
              <div className={styles['work-new']}>
                <span>{t('new')}</span>
              </div>
              <WorkSelection isWorkTabActive={isWorkTabActive} />
            </div>
          )}
          
        </div>
        {solutionHubEnabled && (
          <Link legacyBehavior href={`${solutionsHubURL}${locale}`}>
            <a>
              <div className={styles["solutions-hub"]}>
                <img src={`${imageBaseUrl}/images/union.svg`} />
              </div>
            </a>
          </Link>
        )}
        <div className={styles["tab-selection__spacer"]} />
      </header>

      {/* Tab Content */}
      <div
        className={styles["tab-content-wrapper"]}
        style={{
          backgroundColor:
            activeTabData?.bgColor ||
            "var(--after-bg-color)" ||
            "var(--default-bg-color)",
        }}
      >
        <div className={styles["tab-content"]}>{children}</div>
      </div>
      {page === "offers" && (
        <div ref={contentRef}><HomeDownloadApp /></div>
      )}
      {page === "home" && (
        <>
          {widgetsLineup?.map(({ widget }: any, index: number) => (
            <div className="widgets" key={index} ref={contentRef}>
              {widget === WIDGET_TYPE.DOWNLOAD_APP ? (
                <div ref={ref}>
                  <HomeDownloadApp />
                </div>
              ) : ""}
            </div>
          ))}
       
          <CustomerReviews />
          <SolutionHUbBanner />
        </>
      )}
      {/* Site Footer */}
      {footerData && <Footer ref={footerRef}  footerData={footerData}></Footer> }
    </>
  );
});

TabSelectionHeader.displayName = 'TabSelectionHeader';

export default TabSelectionHeader;
