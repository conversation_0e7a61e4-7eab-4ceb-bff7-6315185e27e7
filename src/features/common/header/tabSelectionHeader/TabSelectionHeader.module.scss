@import "@styles/variables";
@import "@styles/mixins";

.tab-selection {
  position: sticky;
  z-index: 1000;
  top: 0;
  background: white;

  &__spacer {
    display: block;
    height: 16px;
    width: 100%;
    background-color: var(--after-bg-color, transparent);
  }

  &::before,
  &::after {
    content: "";
    height: 24px;
    width: 24px;
    background: transparent;
    position: absolute;
    bottom: -24px;
    border-top-left-radius: 50px;
    box-shadow: -5px -6px 0px 2px var(--after-bg-color, transparent);
  }

  &::before {
    left: 16px;
  }

  &::after {
    right: 16px;
    transform: scaleX(-1);
  }
  
  //#. hide top curve elements while device height is small and scrolled to bottom.
  &--hide-pseudo {
    &::before,
    &::after {
      display: none;
    }
  }
}

.wrapper {
  max-width: 1448px;
  margin: 0 auto;
  background: $white;
  padding-top: 19px;
  position: sticky;
  z-index: 1000;
  top: 0;

  @media (max-width: ($md + 40)) {
    top: -1px;
    box-shadow: 0 3px 6px 0 rgba(92, 99, 105, 10%);
  }

  $breakpoints: (
    1500px: 1400px,
    ($xxl + 40): 1320px,
    ($xl + 40): 1250px,
    $lg: 1170px,
      1200px: 1100px,
      1130px: 1060px,
    );

  @each $breakpoint, $maxWidth in $breakpoints {
    @media (max-width: $breakpoint) {
      max-width: $maxWidth;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  width: 100%;

  &__left {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  &__cart-selection {
    display: flex;
    gap: 16px;
    align-items: center;

    @include rtl-styles{
      @media (max-width: ($lg - 10)) {
        gap: 8px;
      }
    }
  }

  &__cart{
    min-width: 32px;
    min-height: 32px;
  }

  &__right {
    margin-left: auto;
    margin-right: 42px;
    position: relative;

    @media (max-width: 1160px) {
      margin-right: 30px;
    }

    @include rtl-styles {
      margin-left: unset;
      margin-right: auto;
      margin-left: 42px;

      @media (max-width: (1200px)) {
        margin-left: 20px;
      }

      @media (max-width: (1140px)) {
        margin-left: 30px;
      }
    }
  }
}

.logo {
  height: 52px;
  width: 78px;

  &__image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.tab-content {
  background: white;
  position: relative;
  &-wrapper {
    min-height: 100vh;
    padding: 0 16px 16px 16px;
  }
  &::before{
    content: "";
    position: absolute;
    left: 0px;
    bottom: 0;
    height: 24px;
    width: 24px;
    background-color: transparent;
    z-index: 1;
    box-shadow: -5px 6px 0px 2px var(--after-bg-color);
    border-bottom-left-radius: 50px;
  }

  &::after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: 0;
    height: 24px;
    width: 24px;
    background-color: transparent;
    z-index: 1;
    box-shadow: 5px 6px 0px 2px var(--after-bg-color);
    border-bottom-right-radius: 50px;
  }

  margin: 0 auto;
  min-height: 100vh;
  // border-bottom-left-radius: 50px;
  // border-bottom-right-radius: 50px;
}

.solutions-hub{
  display: inline-flex;
  height: 50px;
  padding: 0px 16px;
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
  background: linear-gradient(90deg, #190A21 -14.48%, #7D2F89 62.22%);
  bottom: 32px;
  right: 0px;
  z-index: 10000;
  width: 54px;
  position: absolute;
  cursor: pointer;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;

  img{
    width: 22.109px;
    height: 24px;
    aspect-ratio: 22.11/24.00;
  }

  @include rtl-styles {
    right: unset;
    left: 0px;
    border-top-left-radius: unset;
    border-bottom-left-radius: unset;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
  }
}

.work-new{
  position: absolute;
  top: -8px;
  left: 12px;
  z-index: 100000;
  width: 46px;
  height: 18px;
  display: inline-flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px 12px 12px 2px;
  @include rtl-styles {
      border-radius: 12px 12px 2px 12px;

  }
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.00) 52.5%, rgba(255, 255, 255, 0.80) 100%), linear-gradient(140deg, #F8450B 17.29%, #960A3C 70.45%);
  box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.15) inset;

  @include rtl-styles {
    left: unset;
    right: 12px;
  }

  span{
    color: var(--White-White---Primary, #FFF);
    text-align: center;
    font-family: "Mona Sans";
    font-size: 12px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    letter-spacing: 0.12px;
    text-transform: uppercase;
    
  }
}