import { PAGEURLS } from "@constants/common";
import Link from "next/link";
import React from "react";
import styles from "./WorkSelection.module.scss";
import { useTranslation } from "next-i18next";
import getConfig from "next/config";
import useAppRouter from "@features/common/router.context";


const {
  publicRuntimeConfig: { atWorkUrl }
} = getConfig();

export const WorkSelection = ({
  isWorkTabActive,
}: {
  isWorkTabActive: boolean;
}) => {
  // #. Get translations
  const { t } = useTranslation("common");
  const {
    state: { locale, activeRegion },
    redirect,
    router,
  } = useAppRouter();

    // #. Get the current region
    const currentRegion = activeRegion;
    const currentRegionCode =
      currentRegion?.node?.country?.code?.toLowerCase() || "ae";

  const atWorkSiteUrl = `${atWorkUrl}${PAGEURLS.WORK}${locale}-${currentRegionCode}`;


  return (
    <Link legacyBehavior href={atWorkSiteUrl}>
     <a>
     <div
        className={
          isWorkTabActive
            ? `${styles["tab-selection-work"]} ${styles["tab-selection-work--active"]}`
            : styles["tab-selection-work"]
        }
      >
        <div
          className={`${styles["tab-selection-work__extension"]} ${styles["tab-selection-work__extension--right"]}`}
        />
        <h1>
          <span>@</span>
          {t("work")}
        </h1>
        {isWorkTabActive && (
          <div
            className={`${styles["tab-selection-work__extension"]} ${styles["tab-selection-work__extension--left"]}`}
          />
        )}
      </div>
     </a>
    </Link>
  );
};
