import React, { useEffect, useRef, useState, useCallback } from "react";
import styles from "./ImageSlider.module.scss";
import isAbsoluteURL from "@utils/isAbsoluteURL";
import useAppRouter from "../router.context";
import { Skeleton } from "@mui/material";
import gsap from 'gsap';

const MAX_VISIBLE_SEGMENTS = 3;
const AUTOPLAY_DELAY = 3000; // Define a constant for the autoplay delay

const ImageSlider = ({ images }: any) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [previousIndex, setPreviousIndex] = useState(0);
  const [isFading, setIsFading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const bannerRef = useRef(null);
  const stopAutoplayRef = useRef(false);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isPaused, setIsPaused] = useState(false);
  const [progress, setProgress] = useState(0); 
  const startTimeRef = useRef<number>(Date.now()); 
  const pausedTimeRef = useRef<number>(0);

  const {
    router,
    state: { locale }
  } = useAppRouter();

  const segmentWidth =
    images.length <= MAX_VISIBLE_SEGMENTS
      ? 100 / images.length
      : 100 / MAX_VISIBLE_SEGMENTS;

  const updateIndex = useCallback((index: number) => {
    if (index === currentIndex) return;
    setProgress(0);
    startTimeRef.current = Date.now();
    pausedTimeRef.current = 0;

    setPreviousIndex(currentIndex);
    setCurrentIndex(index);
    setIsFading(true);

    setTimeout(() => {
      setIsFading(false);
    }, 250);
  }, [currentIndex]);

  const nextImage = useCallback(() => {
    updateIndex((currentIndex + 1) % images.length);
  }, [currentIndex, images.length, updateIndex]);

  const clearAutoplay = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  const startAutoplay = useCallback(() => {
    clearAutoplay();
    if (progress === 0) {
      startTimeRef.current = Date.now();
      pausedTimeRef.current = 0;
    }
    const remainingTime = (1 - progress / 100) * AUTOPLAY_DELAY;
    timeoutRef.current = setTimeout(() => {
      if (!isPaused && !stopAutoplayRef.current) {
        nextImage();
      }
    }, remainingTime);
    progressIntervalRef.current = setInterval(() => {
      if (!isPaused && !stopAutoplayRef.current) {
        const now = Date.now();
        const elapsed = now - startTimeRef.current - pausedTimeRef.current;
        const newProgress = Math.min((elapsed / AUTOPLAY_DELAY) * 100, 100);
        setProgress(newProgress);
        if (newProgress >= 100) {
          nextImage();
        }
      }
    }, 50);
  }, [progress, isPaused, nextImage]);

  useEffect(() => {
    if (!isPaused && !stopAutoplayRef.current) {
      startAutoplay();
    }
    return () => clearAutoplay();
  }, [currentIndex, isPaused, startAutoplay]);

  useEffect(() => {
    if (isLoaded) {
      const banner = bannerRef.current;

      gsap.set(banner, {
        transformOrigin: 'top center',
        rotationX: -30,
      });

      gsap.to(banner, {
        rotationX: 0,
        duration: 2.2,
        ease: 'power4.out',
      });
    }
  }, [isLoaded]);

  useEffect(() => {
    setCurrentIndex(0)
    if (!images || images.length === 0) return;

    let loadedCount = 0;

    images.forEach((image: any) => {
      const img = new Image();
      img.src = image?.imageWebp || image?.image || "";
      img.onload = () => {
        loadedCount += 1;
        if (loadedCount === images.length) {
          setIsLoaded(true);
        }
      };
      img.onerror = () => {
        loadedCount += 1;
        if (loadedCount === images.length) {
          setIsLoaded(true);
        }
      };
    });
  }, [images]);

  if (!isLoaded || !images || images.length === 0) {
    return <Skeleton variant="rectangular" width={384} height={450} sx={{ borderRadius: '24px' }} />
  }

  return (
    <div className={styles.slider} ref={bannerRef} style={{ perspective: 1000 }}
      onMouseEnter={() => {
        if (!isPaused) {
          const now = Date.now();
          const elapsed = now - startTimeRef.current - pausedTimeRef.current;
          setProgress(Math.min((elapsed / AUTOPLAY_DELAY) * 100, 100));
          setIsPaused(true);
          clearAutoplay();
        }
      }}
      onMouseLeave={() => {
        if (isPaused) {
          const now = Date.now();
          const pauseStartTime = startTimeRef.current + (progress / 100) * AUTOPLAY_DELAY + pausedTimeRef.current;
          pausedTimeRef.current += now - pauseStartTime;
          setIsPaused(false);
          startAutoplay();
        }
      }}>
      <div className={styles["progress-container"]}>
        {images.map((_: any, index: any) => (
          <div
            key={`segment-${index}`}
            className={`${styles["bar-segment"]} ${index === currentIndex ? styles["active"] : ""
              }`}
            style={{ width: `${segmentWidth}%` }}
            onClick={() => {
              updateIndex(index);
              startAutoplay();
            }}
          >
            {index === currentIndex && (
              <div
                className={`${locale === 'en' ? styles["progress-bar"] : styles["progress-bar-arabic"]}`}
                style={{
                  transform: `scaleX(${progress / 100})`,
                  transformOrigin: locale === 'en' ? 'left' : 'right'
                }}
              />
            )}
          </div>
        ))}
      </div>

      <div className={styles["image-container"]}>
        <img
          src={images && images.length && images?.[previousIndex]?.imageWebp}
          alt={`Previous Slide`}
          className={`${styles["main-image"]} ${isFading ? styles["fade-out"] : styles["hidden"]}`}
        />
        <img
          onClick={() => {
            stopAutoplayRef.current = true;
            clearAutoplay();
            setIsPaused(true);

            const url = images?.[currentIndex]?.url;
            if (isAbsoluteURL(url)) {
              location.href = url;
            } else {
              router.push(url);
            }
          }}
          src={images && images.length && images?.[currentIndex]?.imageWebp}
          alt={`Slide ${currentIndex + 1}`}
          className={`${styles["main-image"]} ${!isFading ? styles["fade-in"] : styles["hidden"]}`}
        />
      </div>
    </div>
  );
};

export default ImageSlider;
