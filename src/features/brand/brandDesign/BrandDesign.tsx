import { jsx } from "@emotion/react";
import React, { useContext, useRef, useState } from "react";
import styles from "./BrandDesign.module.scss";
import { useTranslation } from "next-i18next";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import getConfig from "next/config";
import DoneIcon from "@mui/icons-material/Done";

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const BrandDesign = ({
  imageGallery,
  brandImageData,
  name,
}: any): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  // #. preload image
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
  //state for active card
  const [active, setActive] = useState<boolean>(false);
  //   const toggleState = () => {
  //     // 👇️ passed function to setState
  //     setActive((current) => !current);
  //   };
  return (
    <div className={styles["brand-design"]}>
      {!!imageGallery?.edges?.length ? (
        <div className={styles["brand-design-contents"]}>
          <h5>{t("selectdesign")}</h5>
          <div className={styles["brand-design__slider"]}>
            {!!imageGallery?.edges?.length ? (
              <Swiper
                // install Swiper modules
                slidesPerView={3}
                spaceBetween={10}
                className={`swiper-item ${styles["swiper-item"]}`}
                // effect="fade"
              >
                <SwiperSlide className={styles["swiper-item-slide"]}>
                  <span className={styles["swiper-item-single-tick"]}>
                    <DoneIcon />
                  </span>
                  <a
                    className={styles["swiper-item-single"]}
                    // onClick={toggleState}
                  >
                    <Image
                      blurDataURL={preloadImage}
                      placeholder="blur"
                      className="rounded"
                      src={brandImageData || preloadImage}
                      alt={name}
                      width={160}
                      height={103}
                      unoptimized={true}
                      layout={"responsive"}
                    />
                  </a>
                </SwiperSlide>
                {imageGallery?.edges?.map(
                  ({ node: { image } }: any, index: number) => (
                    <SwiperSlide key={index}>
                      <a
                        // onClick={toggleState}
                        // className={`rounded ${styles["swiper-item-multiple"]}`}
                        className={`${styles["swiper-item-default"]}
                          ${
                            active
                              ? styles["swiper-item-active"]
                              : styles["swiper-item-multiple"]
                          }`}
                      >
                        <Image
                          blurDataURL={preloadImage}
                          placeholder="blur"
                          alt={name}
                          src={image || preloadImage}
                          className="rounded"
                          width={160}
                          height={103}
                          unoptimized={true}
                          layout={"responsive"}
                        />
                      </a>
                    </SwiperSlide>
                  )
                )}
              </Swiper>
            ) : (
              ""
              // <a className={styles["swiper-item-single"]}>
              //   <Image
              //     blurDataURL={preloadImage}
              //     className="rounded"
              //     placeholder="blur"
              //     src={brandImageData || preloadImage}
              //     alt={name}
              //     width={160}
              //     height={103}
              //     layout={"responsive"}
              //   />
              // </a>
            )}
          </div>
        </div>
      ) : (
        ""
      )}
    </div>
  );
};

export default BrandDesign;
