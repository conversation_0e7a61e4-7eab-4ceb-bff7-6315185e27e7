import styles from "./BrandOfferDialog.module.scss";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import getConfig from "next/config";
import { IBrandOffersNode } from "../interfaces/brandOffers.interface";
import { useTranslation } from "next-i18next";
import useAppRouter from "@features/common/router.context";
import Image from "next/image";
import useBrand from "../brand.context";
import { useEffect, useRef, useState } from "react";
import BrandOfferAccordian from "./BrandOfferAccordian";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import type { Swiper as SwiperType } from 'swiper';
import getDirection from "@utils/getDirection";
import useBrandAPI from "../brandAPI";
import OffersDialogSkeleton from "../contentLoader/offersDialogSkeleton/OffersDialogSkeleton";

interface IOfferDialog {
  offerData: IBrandOffersNode;
  brandImage?: string;
  onClose: () => void;
  genericBrandCode?: string;
  isGeneric?: boolean;
}

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method BrandOfferDialog
 * @description Offer details dialog
 * @returns {JSX.Element}
 */
const BrandOfferDialog = ({
  offerData,
  onClose,
  brandImage,
  genericBrandCode,
  isGeneric,
}: IOfferDialog): JSX.Element => {
  // translations
  const { t } = useTranslation("common");
  const {
    state: { card },
  } = useBrand();
  // #. Get Locale
  const {
    state: { locale },
  } = useAppRouter();

  const { fetchOffersAndPromo } = useBrandAPI();

  const {
    offersAndPromoDataLoading,
    offersAndPromoData,
    offersAndPromoDataError,
  } = fetchOffersAndPromo(
    offerData?.node?.brand?.code,
    genericBrandCode || "",
    isGeneric || false
  );
  
  // setup for current direction
  const direction = getDirection(locale);

  /**
   * Swiper navigation HTML div Element reference
   */
  const prevRef = useRef<HTMLDivElement>(null);
  const nextRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<SwiperType | null>(null);

  //images
  const close = `${imageBaseUrl}/icons/close-svg.svg`;
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;
  const errorIcon = `${imageBaseUrl}/icons/error-red.svg`;
  const arrow = `${imageBaseUrl}/icons/revamp/back-arrow.svg`;

  const [accordionExpanded, setAccordionExpanded] = useState<any>(false);
  const [activeIndex, setActiveIndex] = useState(1);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [swiper, setSwiper] = useState<SwiperType | null>(null);
  const OFFER_TYPE_PROMO = "PROMO_CODE";

  /**
   * @description: accordian onChange
   */
  const handleAccordionChange = (id: string) => {
    setAccordionExpanded((prev: string | null) => (prev === id ? null : id));
  };

  /**
   * @method handleClose
   */
  const handleClose = () => {
    onClose && onClose();
  };

  const offersAndPromoCodes = isGeneric
    ? offersAndPromoData?.offersAndPromoCodes?.edges
    : offerData;

    const hasMultipleOffers = offersAndPromoCodes?.length !== 1 && isGeneric ? true : false;

  useEffect(() => {
    if (nextRef.current && prevRef.current) {
      // #. update swiper navigation when refs are ready
      swiper?.navigation?.update();
    }
  }, [nextRef, prevRef]);

    // Function to handle prev slide
    const handlePrev = () => {
      if (sliderRef.current && !sliderRef.current.isBeginning) {
        sliderRef.current.slidePrev();
      }
    };
  
    // Function to handle next slide
    const handleNext = () => {
      if (sliderRef.current && !sliderRef.current.isEnd) {
        sliderRef.current.slideNext();
      }
    };

    // Function to handle changes in the Swiper's position
    const handleSwiperChange = (swiper: any) => {
      setActiveIndex(swiper.activeIndex + 1);
      setIsBeginning(swiper.isBeginning);
      setIsEnd(swiper.isEnd);
    };

  const renderOfferContent = (item: any) => (
    <>
      <DialogTitle>
        <div className={`${styles["offer-dialog__title"]} offer-dialog__title`}>
          <div className={`${styles["offer-dialog__title-content"]} ${hasMultipleOffers && styles['offer-dialog__title-content--multiple']}`}>
            <div className={`${styles["offer-dialog__title-ribbon"]}`}>
              <h5>{t("offer")}</h5>
            </div>
            <div onClick={handleClose} className={`${styles["offer-dialog__close"]}`}>
              <img src={close} alt="close icon" />
            </div>
            {hasMultipleOffers && (
              <div className={`${styles["offer-dialog__pagination"]}`}>
                <div className={`${styles["offer-dialog__pagination-container"]} ${isBeginning && styles['disabled-prev-btn']} ${isEnd && styles['disabled-next-btn']}`}>
                  <div
                    className={styles["swiper-button-prev"]}
                    onClick={handlePrev}
                  >
                    <img src={arrow} alt="close icon" />
                  </div>
                  <p>{activeIndex}</p> &nbsp;
                  <span>{t("of")}</span> &nbsp;
                  <p>{offersAndPromoCodes?.length}</p> &nbsp;
                  <p>{t("offers")}</p>
                  <div
                    className={styles["swiper-button-next"]}
                    onClick={handleNext}              >
                      <img src={arrow} alt="close icon" />
                  </div>
                </div>
              </div>
            )}
            <div className={`${styles["offer-dialog__title-top-bg"]}`}></div>
          </div>

          <div className={styles["offer-dialog__title-content-text"]}>
            <div className={styles["offer-dialog__title-content-text-top"]}>
              <h3>{item?.node?.offerHeader}</h3>
              <p className={styles["offer-dialog__offer-text"]}>
                {item?.node?.offerText}
              </p>
              {item?.node?.sponsorPaymentLabel && (
                <p className={styles["offer-dialog__sponsor-label"]}>
                  {item?.node?.sponsorPaymentLabel}
                </p>
              )}
              {isGeneric && (
                <span>
                  {t(
                    item?.node?.offerType === OFFER_TYPE_PROMO
                      ? "onOfferBrand"
                      : "offerCard",
                    {
                      brandName: offerData?.node?.brand?.name,
                    }
                  )}
                </span>
              )}
            </div>
            {isGeneric ? (
              <div className={styles["offer-dialog__title__image-container"]}>
                <div
                  className={
                    styles["offer-dialog__title__image-container-brand"]
                  }
                >
                  <Image
                    blurDataURL={preloadImage}
                    placeholder="blur"
                    src={brandImage || preloadImage}
                    alt="image"
                    width={188}
                    height={120}
                    layout={"responsive"}
                    unoptimized={true}
                    className={
                      styles["offer-dialog__title__image-container-brand-img"]
                    }
                  />
                </div>
                <div
                  className={
                    styles["offer-dialog__title__image-container-offer"]
                  }
                >
                  <Image
                    blurDataURL={preloadImage}
                    placeholder="blur"
                    src={item?.node?.brand?.brandImageData || preloadImage}
                    alt="image"
                    width={188}
                    height={120}
                    layout={"responsive"}
                    unoptimized={true}
                    className={
                      styles["offer-dialog__title__image-container-offer-img"]
                    }
                  />
                </div>
              </div>
            ) : (
              <div
                className={styles["offer-dialog__non-generic-image-container"]}
              >
                <Image
                  blurDataURL={preloadImage}
                  placeholder="blur"
                  src={brandImage || offerData?.node?.brand?.productImageData || preloadImage}
                  alt="image"
                  width={188}
                  height={120}
                  layout={"responsive"}
                  unoptimized={true}
                  className={
                    styles["offer-dialog__title__image-container-brand-img"]
                  }
                />
              </div>
            )}
          </div>
        </div>
      </DialogTitle>
      <DialogContent>
        <div className={styles["offer-dialog__bottom-cont"]}>
          <div
            className={`${styles["offer-dialog__details"]} ${styles["offer-dialog__divider"]}`}
          >
            {item?.node?.validityText && (
              <div>
                <p>{t("expiresOn")}</p>
                <h5>{item?.node?.validityText}</h5>
              </div>
            )}
            {item?.node?.applicableAmountText && (
              <div>
                <p className={styles["offer-dialog__details__applicable"]}>
                  {t("applicableAmounts")}{" "}
                </p>
                <h5>{item?.node?.applicableAmountText}</h5>
              </div>
            )}
          </div>

          {item?.node?.node?.disableWarningText ? (
            <div className={styles["offer-dialog__offer-exhausted"]}>
              <img src={errorIcon} />
              <p>{item?.node?.disableWarningText}</p>
            </div>
          ) : (
            <div className={styles["offer-dialog__title-works"]}>
              <h4>{t("worksTitle")}</h4>
              <span>
                1.
                {isGeneric ? (
                  <p>{t("worksText", { brandName: card?.brandName })}</p>
                ) : (
                  <p>{t("selectMokafaPoint",{payment:item?.node?.sponsorPaymentNetwork?.toLowerCase()})}</p>
                )}
              </span>
              <span>
                2.
                {isGeneric ? (
                  <p>
                    {t("spendText", {
                      brandName: offerData?.node?.brand?.name,
                    })}
                  </p>
                ) : (
                  <p>{t("payUsingMokafa",{payment:item?.node?.sponsorPaymentNetwork?.toLowerCase()})}</p>
                )}
              </span>
              <span>
                3.
                <p>
                  {item?.node?.offerType === OFFER_TYPE_PROMO
                    ? t("promoText",{brandName: offerData?.node?.brand?.name})
                    : t("valueText", {
                        brandName: offerData?.node?.brand?.name,
                      })}
                </p>
              </span>
            </div>
          )}

          <BrandOfferAccordian
            offerTermsArray={item?.node?.offerTerms}
            onAccordionChange={handleAccordionChange}
            accordionExpanded={accordionExpanded === "offerTerms"}
            id="offerTerms"
            title={t("termsTitle")}
          />
          
            <BrandOfferAccordian
              offerTermsArray={item?.node?.brand?.description}
              onAccordionChange={handleAccordionChange}
              accordionExpanded={accordionExpanded === "brandDescription"}
              id="brandDescription"
              title={t("aboutThisGift")}
              />
        </div>
      </DialogContent>
    </>
  );

  return (
    <div>
      <Dialog
        open={true}
        onClose={handleClose}
        className={`${styles["offer-dialog"]} offer-dialog`}
      >
        {isGeneric ? (
          <Swiper
            dir={direction}
            spaceBetween={0}
            slidesPerView={1}
            allowTouchMove={false}
            modules={[Autoplay, Pagination, Navigation]}
            onInit={(swiper) => {
              setSwiper(swiper);
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.prevEl = prevRef.current;
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              // eslint-disable-next-line no-param-reassign
              swiper.params.navigation.nextEl = nextRef.current;
              swiper.navigation.update();
            }}
            className="swiper-offers-dialog"
            navigation
            onSwiper={(swiper) => {
              sliderRef.current = swiper;
            }}
            onSlideChange={(swiper: any) =>{
              handleSwiperChange(swiper)
              setActiveIndex(swiper.activeIndex + 1)
          }}
          >
            {offersAndPromoDataLoading && <OffersDialogSkeleton />}
            {!offersAndPromoDataError && offersAndPromoCodes
              ? offersAndPromoCodes?.map((item: any, index: string) => (
                  <SwiperSlide key={index}>
                    {" "}
                    {renderOfferContent(item)}{" "}
                  </SwiperSlide>
                ))
              : ""}
          </Swiper>
        ) : (
          renderOfferContent(offersAndPromoCodes)
        )}
      </Dialog>
    </div>
  );
};

export default BrandOfferDialog;
