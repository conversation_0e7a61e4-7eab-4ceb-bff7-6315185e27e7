@import "@styles/variables";
@import "@styles/mixins";

.grid-slider {
  img {
    display: block;
    width: 108px;
    height: 147px;
    border-radius: 12px;

    @media (max-width: ($xl + 40)) {
      width: 104px;
      height: 140px;
    }
  }

  .select-box {
    z-index: 100;
    position: absolute;
    background-color: #fff;
    width: 30px;
    height: 30px;
    top: -1px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom-left-radius: 12px;
    &__single-tick {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 20px;
      width: 20px;
      background-color: $barney-purple;
      border-radius: 50%;
      color: #fff;
    }
    svg {
      fill: $white;
    }
  }
  .active {
    box-shadow: 0 4px 16px 0 rgba(69, 91, 99, 8%);
    @include rtl-styles {
      // box-shadow: none;
    }
  }
  .hide {
    display: none;
  }
}
