import styles from "./BrandGridSlider.module.scss";
import Image from "next/image";
import getConfig from "next/config";
import DoneIcon from "@mui/icons-material/Done";

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const BrandGridSlider = ({
  gif,
  data,
  onActive,
  selected,
  hasPersonalisedContent,
}: any): JSX.Element => {
  return (
    <div
      className={`${styles["grid-slider"]} ${
        selected === (gif ? data?.node?.gifFile : data?.node?.cardImage)
          ? styles["active"]
          : ""
      } `}
      key={gif ? data?.node?.gifFile : data?.node?.cardImage}
    >
      <>
        <Image
          src={gif ? data?.node?.gifFile : data?.node?.cardImage}
          blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
          placeholder="blur"
          width={108}
          height={147}
          unoptimized={true}
          alt="Greeting image"
          onClick={() => {
            onActive(data, gif);
          }}
          data-testid="gridSliderImage"
          id="e2eTestinggridSliderImage"
          loading="eager"
          priority={true}
        />
      </>

      {hasPersonalisedContent && (
        <div
          className={`${
            selected === (gif ? data?.node?.gifFile : data?.node?.cardImage)
              ? styles["select-box"]
              : styles["hide"]
          }`}
        >
          <span className={styles["select-box__single-tick"]}>
            <DoneIcon />
          </span>
        </div>
      )}
    </div>
  );
};

export default BrandGridSlider;
