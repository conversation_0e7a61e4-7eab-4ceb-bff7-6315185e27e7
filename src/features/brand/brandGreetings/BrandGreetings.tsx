import { useEffect, useMemo, useState } from "react";
import styles from "./BrandGreetings.module.scss";
import { useTranslation } from "next-i18next";
import MenuItem from "@mui/material/MenuItem";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import { useQuery } from "@apollo/client";
import {
  GreetingsOccasionInterface,
  GreetingsIllustrationsInterface,
} from "../interfaces/greetings.interface";
import {
  BRAND_OCCASIONS_QUERY,
  BRAND_ILLUSTRATIONS,
  BRAND_GIF_ILLUSTRATION,
} from "../brand.query";
import {
  Media,
  MediaContextProvider,
} from "@features/common/appMedia/AppMedia";

import GreetingsSwiper from "./brandGreetingsSwiper/BrandGreetingsSwiper";
import useBrand, { BrandContextAction } from "../brand.context";
import {
  BRAND_FORM_STATE,
  BRAND_STEPPER,
  BRAND_STEPPER_STATE,
  GREETING_COVER_TYPE,
  GREETING_LANGUAGE,
} from "../constants/brand.constant";
import BrandGreetingsSkelton from "../contentLoader/brandGreetingsSkelton/BrandGreetingsSkelton";
import Button from "@features/common/button/Button";
import BrandSelectedValueDisplay from "../brandSelectedValueDisplay/BrandSelectedValueDisplay";
import useAppRouter, { DEFAULT_REGION } from "@features/common/router.context";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import StepContent from "@mui/material/StepContent";
import { ApolloError } from "@apollo/client";
import getConfig from "next/config";
import { cleverTapService } from "@features/common/clevertap.services";
import GoogleTagManager from "@utils/googleTagManager";

enum SKIPPED_OCCASION_CODE {
  CODE = "CUS",
  NAME = "Custom",
}

/**
 * @method BrandGreetings
 * @description Greetings slider component
 * @returns
 */
const BrandGreetings = (): JSX.Element => {
  // #. Get translations
  const { t } = useTranslation("common");

  // #. Taking public image config url
  const {
    publicRuntimeConfig: { imageBaseUrl },
  } = getConfig();

  // #. Get Locale
  const {
    state: { locale, region, activeRegion },
  } = useAppRouter();

  // button status
  const [buttonDisabled, setButtonDisabled] = useState<boolean>(false);

  const {
    state: { card, stepper },
    dispatch,
  } = useBrand();

  //#. Set is animated or not state
  const [isAnimated, setIsAnimated] = useState(false);
  const [occasion, setOccasion] = useState<any>({});
  const [language, setLanguage] = useState(locale);
  const [coverType, setCoverType] = useState("");
  const [errorMessage, setErrorMessage] = useState<boolean>(false);
  const [changeButtonTitle, setChangeButtonTitle] = useState<any>("");
  const [hasPersonalisedContent, setHasPersonalisedContent] = useState(false);

  const errorImage = `${imageBaseUrl}/icons/error-icon.svg`;

  // #. Get all occasions
  const { loading: occasionLoading, data: occasionData } =
    useQuery<GreetingsOccasionInterface>(BRAND_OCCASIONS_QUERY, {
      // fetchPolicy: "cache-first", // Used for first execution
      variables: {
        gif: isAnimated,
        language: language,
      },
      context: {
        clientName: "greetings",
        headers: {
          "accept-language": language,
        },
      },
      onError: (error: ApolloError) => {
        setErrorMessage(true);
      },
      onCompleted: (response) => {
        setErrorMessage(false);
      },
      skip: !language,
    });

  // #. Get illustartion options
  const { loading: illustrationLoading, data: illustrationData } =
    useQuery<GreetingsIllustrationsInterface>(
      isAnimated === true ? BRAND_GIF_ILLUSTRATION : BRAND_ILLUSTRATIONS,
      {
        variables: {
          code: occasion?.code,
          language: language,
        },
        context: {
          clientName: "greetings",
          headers: {
            "accept-language": language,
          },
        },
        fetchPolicy: "cache-first", // Used for first execution
        skip: !occasion?.code,
      }
    );

  /**
   * @method dispatchBrandContext
   * @description Dispatch the new value details
   * @param value
   */
  const dispatchBrandContext = (value: object) => {
    // #. migrate with new values
    const newValue = {
      greetingCover: { ...card.greetingCover, ...value },
      isDirty: true,
    };

    // #. Dispatch the changes
    dispatch({ type: BrandContextAction.CARD, payload: newValue });
  };

  /**
   * @method dispatchBrandAccordionContext
   * @param value
   */
  const dispatchBrandStepperContext = (payload: any) => {
    dispatch({
      type: BrandContextAction.STEPPER,
      payload,
    });
  };

  /**
   * @method handleOcassion
   * @param occasion
   */
  const handleOcassion = (event: SelectChangeEvent) => {
    const occCode = event.target.value;

    const occasions = findOccasionByCode(occCode);

    occasions && setOccasion(occasions?.node);
  };

  /**
   * @method findOccasionByCode
   * @param code
   */
  const findOccasionByCode = (code: string) => {
    const occasion = occasionData?.occasions?.edges?.find((item) => {
      return item?.node?.code === code;
    });

    return occasion;
  };

  /**
   * @method handleType
   * @param event
   */
  const handleType = (event: SelectChangeEvent) => {
    setCoverType(event.target.value);

    // #. Update the gif or not state
    setIsAnimated(event.target.value === GREETING_COVER_TYPE.ANIMATED);
  };

  /**
   * @method handlelanguage
   * @param event
   */
  const handlelanguage = (event: SelectChangeEvent) => {
    setLanguage(event.target.value);
  };

  /**
   * @method onGreetingSelected
   * @param value
   */
  const onGreetingSelected = (value: any) => {
    setButtonDisabled(value.filePath == null);

    // #. Dispatch the changes
    dispatchBrandContext({
      filePath: value.filePath,
      referenceCode: value?.referenceCode,
      staticGifPath: value?.staticGifPath,
      occasion,
      language,
      coverType,
    });

    //clevertap function
    updateCleverTap();

    // #. GTM Event
    gtmOccasionEvent();
  };

  /**
   * @method updateCleverTap
   */
  const updateCleverTap = () => {
    const greetingsData = {
      productName: card.brandName,
      productId: card.brandCode,
      store: activeRegion?.node.country.code,
      occasion: occasion.code,
      greetingId: occasion.code,
    };
    cleverTapService.pushTappedOnSelectOccasion(greetingsData);
  };

  /**
   * @method gtmOccasionEvent
   */
  const gtmOccasionEvent = () => {
    // #.Push data to GTM
    const { push: gtmPush } = GoogleTagManager();

    let gtmData = {
      item_name: card.brandName,
      item_id: card.brandCode,
      occasion: occasion.code,
    };

    gtmPush("select_occasion", {
      store: region?.toUpperCase() || DEFAULT_REGION?.toUpperCase(),
      items: [{ ...gtmData }],
    });
  };

  /**
   * @method onContinueClicked
   */
  const onContinueClicked = () => {
    const perosnalisedState =
      stepper[BRAND_STEPPER.PERSONALIZE].stepState ===
      BRAND_STEPPER_STATE.NOT_OPENED
        ? BRAND_STEPPER_STATE.ACTIVE
        : stepper[BRAND_STEPPER.PERSONALIZE].stepState;

    dispatchBrandStepperContext({
      activeStep:
        stepper.openStep === BRAND_STEPPER.GREETING
          ? BRAND_STEPPER.PERSONALIZE
          : stepper.openStep,
      [BRAND_STEPPER.GREETING]: {
        stepState: !hasPersonalisedContent
          ? BRAND_STEPPER_STATE.SKIPPED
          : BRAND_STEPPER_STATE.COMPLETED,
      },
      [BRAND_STEPPER.PERSONALIZE]: {
        stepState: perosnalisedState,
      },
    });
  };

  const activeStepperState = stepper[BRAND_STEPPER.GREETING].stepState;
  const isSkipped = activeStepperState === BRAND_STEPPER_STATE.SKIPPED;
  const isExpanded = stepper.activeStep === BRAND_STEPPER.GREETING;

  /**
   * @method updateChangeButtonTitle
   */
  const updateChangeButtonTitle = (
    isExpanded: boolean,
    hasContent: boolean
  ) => {
    if (!isExpanded && !hasContent) {
      setChangeButtonTitle(t("add"));
    } else if (!isExpanded && hasContent) {
      setChangeButtonTitle(t("change"));
    } else if (isExpanded && !hasContent) {
      setChangeButtonTitle(t("skip"));
    } else if (isExpanded && hasContent) {
      setChangeButtonTitle(t("skip"));
    } else {
      setChangeButtonTitle("  ");
      return;
    }
  };

  useEffect(() => {
    updateChangeButtonTitle(isExpanded, hasPersonalisedContent);
  }, [isExpanded]);

  useEffect(() => {
    const hasContent =
      Boolean(card?.greetingCover?.filePath) ||
      Boolean(card?.greetingCover?.staticGifPath);

    setHasPersonalisedContent(hasContent);
    updateChangeButtonTitle(isExpanded, hasContent);
  }, [card?.greetingCover?.filePath, card?.greetingCover?.staticGifPath]);

  useEffect(() => {
    dispatchBrandContext({
      openStep: BRAND_STEPPER.GREETING,
    });
  }, []);

  useEffect(() => {
    setLanguage(card?.greetingCover?.language || locale);
    setOccasion(card?.greetingCover?.occasion);
    setCoverType(card?.greetingCover?.coverType || "");
  }, []);

  useEffect(() => {
    if (
      card?.formState === BRAND_FORM_STATE.EDIT &&
      card?.greetingCover?.occasion?.code &&
      !card?.greetingCover?.occasion?.name
    ) {
      const occasion = findOccasionByCode(card?.greetingCover?.occasion?.code);
      setOccasion(occasion?.node);
    }
  }, [occasionLoading, locale]);

  useEffect(() => {
    if (
      card?.formState === BRAND_FORM_STATE.EDIT &&
      card?.greetingCover?.coverType === GREETING_COVER_TYPE.ANIMATED &&
      card?.greetingCover?.occasion?.name
    ) {
      setIsAnimated(true);
    }
  }, []);

  /**
   * @method onHeaderChangeClicked
   */
  const onHeaderChangeClicked = (type: string) => {
    // #. Update the stepper state
    dispatchBrandStepperContext({
      activeStep: BRAND_STEPPER.GREETING,
    });

    if (type === t("skip")) {
      dispatchBrandContext({
        filePath: "",
        referenceCode: "",
        staticGifPath: "",
        occasion: "",
      });

      findOccasionByCode("");

      const greetingState =
        stepper[BRAND_STEPPER.PERSONALIZE].stepState ===
        BRAND_STEPPER_STATE.NOT_OPENED
          ? BRAND_STEPPER_STATE.ACTIVE
          : stepper[BRAND_STEPPER.PERSONALIZE].stepState;

      dispatchBrandStepperContext({
        activeStep:
          stepper.openStep === BRAND_STEPPER.GREETING
            ? BRAND_STEPPER.PERSONALIZE
            : stepper.openStep,
        [BRAND_STEPPER.GREETING]: {
          stepState: BRAND_STEPPER_STATE.SKIPPED,
        },
        [BRAND_STEPPER.PERSONALIZE]: {
          stepState: greetingState,
        },
      });
    }
  };

  const greetingsTitleText =
    card?.formState === BRAND_FORM_STATE.CREATE && isExpanded
      ? t("greetingtitle")
      : !hasPersonalisedContent
      ? t("greetingtitle")
      : `${occasion?.name || ""} ${t("greetings")}`;

  useEffect(() => {
    if (!occasionLoading && occasion) {
      const chosenOcassion = findOccasionByCode(occasion.code);
      chosenOcassion && setOccasion(chosenOcassion?.node);
    }
  }, [occasionLoading]);

  useEffect(() => {
    if (card?.formState === BRAND_FORM_STATE.CREATE) {
      setLanguage(locale);
    }
  }, [locale, card?.formState]);

  // #. mui select icon overide
  const IconComponent = useMemo(() => {
    const CustomIcon = (params: any) => (
      <img
        src={`${imageBaseUrl}/icons/arr-dow.svg`}
        className={styles["dropdown-icon"]}
        height={16}
        width={16}
        {...params}
      />
    );
    CustomIcon.displayName = "IconComponent";
    return CustomIcon;
  }, []);

  return (
    <>
      <Step
        key={"Brand Greetings"}
        expanded={isExpanded}
        className={`brand-stepper__step ${
          stepper[BRAND_STEPPER.GREETING].stepState ===
          BRAND_STEPPER_STATE.ACTIVE
            ? "hide-border"
            : ""
        }`}
      >
        <StepLabel>
          <div className="brand-stepper__step-collapsed-header">
            <BrandSelectedValueDisplay
              title={greetingsTitleText}
              onChangeClicked={onHeaderChangeClicked}
              stepperIconState={activeStepperState}
              hideChangeButton={false}
              stepperId={
                !hasPersonalisedContent && isSkipped
                  ? BRAND_STEPPER.PERSONALIZE
                  : BRAND_STEPPER.UNKNOWN__
              }
              changeButtonTitle={changeButtonTitle}
              isExpandedMode={isExpanded}
            />
          </div>
        </StepLabel>
        {occasionLoading || illustrationLoading ? (
          <BrandGreetingsSkelton />
        ) : (
          <>
            <div
              className={`${styles["greetings"]} brand-accordion`}
              data-testid="greetingsChildren"
            >
              <StepContent TransitionProps={{ unmountOnExit: false }}>
                <div className={styles["greetings-container"]}>
                  <div
                    className={`greetings ${styles["greetings-container__top-container"]}`}
                  >
                    {!occasionLoading && (
                      <Select
                        value={
                          illustrationData?.gifIllustrations?.edges?.length ===
                            0 ||
                          illustrationData?.illustrations?.edges?.length === 0
                            ? "-1"
                            : occasion?.code
                        }
                        className={`${styles["greetings__select-container"]} select-container`}
                        onChange={handleOcassion}
                        inputProps={{ "data-testid": "selectOccasion" }}
                        MenuProps={{
                          classes: {
                            paper: `${styles["select-menu-item"]}`,
                          },
                        }}
                        defaultValue={
                          illustrationData?.illustrations?.edges?.length
                            ? "-1"
                            : ""
                        }
                        IconComponent={IconComponent}
                      >
                        <MenuItem
                          key="-1"
                          value="-1"
                          className={`${styles["greetings__select-container-occasion"]} select-containeroccasion`}
                        >
                          {t("brandSelect")}
                        </MenuItem>
                        {occasionData?.occasions?.edges?.map(
                          (item: any, index: number) => (
                            <MenuItem key={index} value={item.node.code}>
                              {item.node.name}
                            </MenuItem>
                          )
                        )}
                      </Select>
                    )}
                    <Select
                      value={coverType}
                      onChange={handleType}
                      inputProps={{ "data-testid": "selectAnimation" }}
                      // data-testid="selectAnimation"
                      className={styles["select-container-reg"]}
                      MenuProps={{
                        classes: {
                          paper: `${styles["select-menu-item"]}`,
                        },
                      }}
                      defaultValue=""
                      IconComponent={IconComponent}
                    >
                      <MenuItem value={GREETING_COVER_TYPE.STANDARD}>
                        {t("standard")}
                      </MenuItem>
                      <MenuItem value={GREETING_COVER_TYPE.ANIMATED}>
                        {t("animation")}
                      </MenuItem>
                    </Select>
                    <Select
                      value={language}
                      onChange={handlelanguage}
                      inputProps={{ "data-testid": "selectLanguage" }}
                      // data-testid="selectLanguage"
                      className={styles["select-container-reg"]}
                      MenuProps={{
                        classes: {
                          paper: `${styles["select-menu-item"]}`,
                        },
                      }}
                      defaultValue=""
                      IconComponent={IconComponent}
                    >
                      <MenuItem value={GREETING_LANGUAGE.ENGLISH}>
                        {t("english")}
                      </MenuItem>
                      <MenuItem value={GREETING_LANGUAGE.ARABIC}>
                        {t("arabic")}
                      </MenuItem>
                    </Select>
                  </div>
                </div>
                <MediaContextProvider>
                  <Media at="md">
                    <GreetingsSwiper
                      illustrationData={
                        illustrationData?.illustrations?.edges ||
                        illustrationData?.gifIllustrations?.edges
                      }
                      gif={isAnimated}
                      loading={illustrationLoading}
                      slidePerView={10}
                      hasPersonalisedContent={hasPersonalisedContent}
                      onGreetingSelected={onGreetingSelected}
                    />
                  </Media>

                  <Media greaterThanOrEqual="lg">
                    <GreetingsSwiper
                      illustrationData={
                        illustrationData?.illustrations?.edges ||
                        illustrationData?.gifIllustrations?.edges
                      }
                      gif={isAnimated}
                      loading={illustrationLoading}
                      slidePerView={10}
                      hasPersonalisedContent={hasPersonalisedContent}
                      onGreetingSelected={onGreetingSelected}
                    />
                  </Media>
                </MediaContextProvider>
                {illustrationData?.gifIllustrations?.edges?.length === 0 ||
                (illustrationData?.illustrations?.edges?.length === 0 &&
                  occasion?.code !== SKIPPED_OCCASION_CODE.CODE) ? (
                  <p className={styles["empty-container"]}>
                    {t("gretingCover")}
                  </p>
                ) : occasion?.code === SKIPPED_OCCASION_CODE.CODE ? (
                  <p className={styles["empty-container"]}>
                    {t("selectGreetingCover")}
                  </p>
                ) : null}

                {errorMessage && (
                  <>
                    <span className={styles["error-container"]}>
                      <img src={errorImage} alt="Error" />
                      <p className={styles["error-text"]}>{t("error-text")}</p>
                    </span>
                  </>
                )}

                {stepper.activeStep === BRAND_STEPPER.GREETING && (
                  <div className={`brand-button ${styles["brand-button"]}`}>
                    <Button
                      theme="primary"
                      action={onContinueClicked}
                      className="brand-button__continue"
                      attribues={{
                        disabled: buttonDisabled,
                        type: "button",
                      }}
                    >
                      {t("continue")}
                    </Button>
                  </div>
                )}
              </StepContent>
            </div>
          </>
        )}
      </Step>
    </>
  );
};

export default BrandGreetings;
