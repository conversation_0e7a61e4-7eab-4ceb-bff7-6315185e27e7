import React, { useEffect, useState } from "react";
import styles from "./BrandGreetingsSwiper.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay, Grid } from "swiper/modules";
import GridSlider from "../brandGridSlider/BrandGridSlider";
import getDirection from "@utils/getDirection";
import getConfig from "next/config";
import BrandGridSkelton from "@features/brand/contentLoader/brandGridSkelton/BrandGridSkelton";
import useAppRouter from "@features/common/router.context";
import useBrand, { BrandContextAction } from "@features/brand/brand.context";
import {
  BRAND_FORM_STATE,
  BRAND_STEPPER,
} from "@features/brand/constants/brand.constant";

const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const nextImage = `${imageBaseUrl}/icons/arrow-circle-fw.svg`;

const BrandGreetingsSwiper = ({
  illustrationData,
  gif,
  loading,
  slidePerView,
  onGreetingSelected,
  hasPersonalisedContent,
}: any): JSX.Element => {
  // getting locale
  const {
    state: { locale },
  } = useAppRouter();

  const {
    state: { card },
    dispatch,
  } = useBrand();

  // setup for current direction
  const direction = getDirection(locale);

  //state for card count
  const [cardCount, setCardCount] = useState(illustrationData?.length || 0);
  const [active, setActive] = useState(card?.greetingCover?.filePath || "");
  /**
   * @method onActive
   * @description Set selected image
   * @param item
   * @param isGif
   */
  const onActive = (item: any, isGif: boolean) => {
    const activeItem = isGif ? item?.node?.gifFile : item?.node?.cardImage;
    setActive(activeItem);
    // #. Dispatch the selcted details
    const filePath = isGif ? item?.node?.gifFile : item?.node?.cardImage || "";
    const referenceCode = item?.node?.referenceCode || "";
    const staticGifPath = item?.node?.gifImage || "";
    onGreetingSelected &&
      onGreetingSelected({ filePath, referenceCode, staticGifPath });
  };

  useEffect(() => {
    if (card?.formState === BRAND_FORM_STATE.EDIT) {
      dispatch({
        type: BrandContextAction.STEPPER,
        payload: {
          activeStep: BRAND_STEPPER.GREETING,
        },
      });
    }
  }, []);

  return (
    <>
      {loading ? (
        <BrandGridSkelton />
      ) : (
        <div
          className={`greetings ${styles["grid-container"]}`}
          data-testid="swiperWrapper"
        >
          <Swiper
            dir={direction}
            slidesPerView={5}
            slidesPerGroup={3}
            spaceBetween={10}
            navigation={{
              prevEl: `.swiper-button-prev`,
              nextEl: `.swiper-button-next`,
            }}
            pagination={false}
            autoplay={false}
            modules={[Navigation, Autoplay, Grid]}
            grid={{ rows: 2, fill: "row" }}
            breakpoints={{
              1320: {
                slidesPerView: 5,
              },
              1024: {
                slidesPerView: 4,
                spaceBetween: 10,
              },
            }}
          >
            {illustrationData?.map((item: any, index: number) => (
              <SwiperSlide key={index}>
                <GridSlider
                  illustrationData={illustrationData}
                  slidePerView={slidePerView}
                  index={index}
                  gif={gif}
                  data={item}
                  onActive={onActive}
                  selected={active}
                  hasPersonalisedContent={hasPersonalisedContent}
                  loading={loading}
                />
              </SwiperSlide>
            ))}

            <div
              className={`${cardCount < 10 && styles["hide"]} button-container`}
            >
              <div className="swiper-button-next" data-testid="nextNav">
                <img src={nextImage} alt="Image" />
              </div>
              <div className="swiper-button-prev" data-testid="prevNav">
                <img src={nextImage} alt="Image" />
              </div>
            </div>
          </Swiper>
          {/* {cardCount >= 20 && (
            <>
              <h6
                className={styles["grid-container__text"]}
                data-testid="swiperText"
              >
                {cardCount - 2}+ Greeting Card(s)
              </h6>
            </>
          )} */}
        </div>
      )}
    </>
  );
};

export default BrandGreetingsSwiper;
