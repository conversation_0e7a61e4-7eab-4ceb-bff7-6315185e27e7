import React, { useState } from "react";
import styles from "./BrandQuickInfoTab.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, EffectFade, Mousewheel } from "swiper/modules";
import Image from "next/image";
import getConfig from "next/config";
import { useMediaQuery } from "@mui/material";

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();



/**
 * @method BrandQuickInfoTab
 * @description Brand quick info slider component
 * @returns {JSX.Element}
 */
const BrandQuickInfoTab = ({
  imageGallery,
  name,
  brandImageData,
}: any): JSX.Element => {
  // #. preload image
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`; 

  const isTab = useMediaQuery("(max-width:1064px)");

  return (
    <div className={`quick-info-tab-section ${styles["quick-info-tab"]}`} data-testid="quickInfo">
      <div
        className={styles["quick-info-tab__slider"]}
        data-testid="infoItem"
      >
        {!!imageGallery?.edges?.length ? (
          <>
            <Swiper
              // install Swiper modules
              modules={[Autoplay, Pagination, EffectFade, Mousewheel]}
              dir="right"
              slidesPerView={1}
              spaceBetween={10}
              pagination={{ clickable: true }}
              className={`swiper-item ${styles["swiper-item"]}`}
              autoplay={{
                delay: 2000,
                disableOnInteraction: false,
              }}
              mousewheel={{
                releaseOnEdges: true,
              }}
              effect="fade"
              loop={true}
            >
              {imageGallery?.edges?.map(
                ({ node: { image } }: any, index: number) => (
                  <SwiperSlide key={index}>
                    <a className={`rounded ${styles["swiper-item-img"]}`}>
                      <Image
                        blurDataURL={preloadImage}
                        placeholder="blur"
                        alt={name}
                        src={image || preloadImage}
                        width={416}
                        height={266}
                        unoptimized={true}
                        layout={"responsive"}
                        data-testid="quickInfoSliderImage"
                        loading="eager"
                      />
                    </a>
                  </SwiperSlide>
                )
              )}
            </Swiper>
          </>
        ) : (
          <a className={`rounded ${styles["swiper-item-single"]}`}>
            <Image
              blurDataURL={preloadImage}
              placeholder="blur"
              src={brandImageData || preloadImage}
              alt={name}
              width={416}
              height={266}
              unoptimized={true}
              layout={"responsive"}
              data-testid="quickInfoSingleImage"
            />
          </a>
        )}
      </div>
    </div>
  );
};

export default BrandQuickInfoTab;
