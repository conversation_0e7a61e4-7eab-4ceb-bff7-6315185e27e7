import React, { useEffect, useState } from "react";
import styles from "./BrandQuickInfo.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, EffectFade, Mousewheel } from "swiper/modules";
import Image from "next/image";
import getConfig from "next/config";
import Link from "next/link";
import useBrand from "../brand.context";

// taking public image config url
const {
  publicRuntimeConfig: { imageBaseUrl },
} = getConfig();



/**
 * @method BrandQuickInfo
 * @description Brand quick info slider component
 * @returns {JSX.Element}
 */
const BrandQuickInfo = ({
  imageGallery,
  name,
  brandImageData,
}: any): JSX.Element => {
  //getting card data
  const {
    state: { card },
  } = useBrand();

  // #. preload image
  const preloadImage = `${imageBaseUrl}/images/preload-image.jpeg`;

  const [skinImage, setSkinImage] = useState<string>(card?.brandSkinUrl);

  // slider status
  const [sliderEnabled, setSliderEnabled] = useState<boolean>(false);

  const sliderOnMouseEnter = () => {
    // #. Activate the slider
    setSliderEnabled(true);
  };

  const sliderOnMouseLeave = () => {
    setSliderEnabled(false);
  };

  useEffect(() => {
    if (card?.brandSkinUrl?.length >= 3) {
      setSkinImage(card?.brandSkinUrl);
    }
  }, [card]);

  return (
    <div className={styles["quick-info"]} data-testid="quickInfo">
      <div
        className={styles["quick-info__slider"]}
        onMouseEnter={sliderOnMouseEnter}
        onMouseLeave={sliderOnMouseLeave}
        data-testid="infoItem"
      >
        {!!imageGallery?.edges?.length && sliderEnabled ? (
          <>
            <Swiper
              // install Swiper modules
              modules={[Autoplay, Pagination, EffectFade, Mousewheel]}
              dir="right"
              slidesPerView={1}
              spaceBetween={10}
              pagination={{ clickable: true }}
              className={`swiper-item ${styles["swiper-item"]}`}
              autoplay={{
                delay: 2000,
                disableOnInteraction: false,
              }}
              mousewheel={{
                releaseOnEdges: true,
              }}
              effect="fade"
              loop={true}
            >
              {imageGallery?.edges?.map(
                ({ node: { image } }: any, index: number) => (
                  <SwiperSlide key={index}>
                    <a className={`rounded ${styles["swiper-item-img"]}`}>
                      <Image
                        blurDataURL={preloadImage}
                        placeholder="blur"
                        alt={name}
                        src={skinImage ? skinImage : image || preloadImage}
                        width={416}
                        height={266}
                        unoptimized={true}
                        layout={"responsive"}
                        data-testid="quickInfoSliderImage"
                        loading="eager"
                      />
                    </a>
                  </SwiperSlide>
                )
              )}
            </Swiper>
          </>
        ) : (
          <Link legacyBehavior href="#">
            <a className={`rounded ${styles["swiper-item-single"]}`}>
              <Image
                blurDataURL={preloadImage}
                placeholder="blur"
                src={skinImage ? skinImage : brandImageData || preloadImage}
                alt={name}
                width={416}
                height={266}
                unoptimized={true}
                layout={"responsive"}
                data-testid="quickInfoSingleImage"
              />
            </a>
          </Link>
        )}
      </div>
    </div>
  );
};

export default BrandQuickInfo;
