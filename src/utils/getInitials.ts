/**
 * Generates initials from a given name.
 * - If both first and last names exist, returns their initials.
 * - If only the first name exists, returns its first letter.
 *
 * @param {string} name - The full name of the user.
 * @return {string} The initials extracted from the name.
 */
export const getInitials = (name:string) => {
    if (!name) return "";
  
    const parts = name.trim().split(/\s+/);
    const firstInitial = parts[0]?.charAt(0).toUpperCase() || "";
  
    if (parts.length > 1) {
      const lastInitial = parts[parts.length - 1]?.charAt(0).toUpperCase() || "";
      return firstInitial + lastInitial;
    }
  
    return firstInitial;
  };
  